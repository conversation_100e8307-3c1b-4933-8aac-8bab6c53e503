package org.springblade.modules.auth.request;

import com.alibaba.fastjson.JSONObject;
import me.zhyd.oauth.cache.AuthStateCache;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.enums.AuthUserGender;
import me.zhyd.oauth.exception.AuthException;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthToken;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthDefaultRequest;
import me.zhyd.oauth.utils.HttpUtils;
import me.zhyd.oauth.utils.UrlBuilder;
import org.springblade.modules.auth.authsource.IdaasAuthSource;

import java.net.URLEncoder;

public class IdaasRequest extends AuthDefaultRequest {

    private String idaasServer;

    public IdaasRequest(AuthConfig config, AuthStateCache authStateCache, String idaasServer) {
        super(config, IdaasAuthSource.IDAAS, authStateCache);
        this.idaasServer = idaasServer;
    }

    @Override
    public AuthToken getAccessToken(AuthCallback authCallback) {
        String url = this.source.accessToken().replace("{IDaaS_server}", this.idaasServer);
        url = UrlBuilder.fromBaseUrl(url)
                .queryParam("grant_type", "authorization_code")
                .queryParam("code", authCallback.getCode())
                .queryParam("redirect_uri", URLEncoder.encode(config.getRedirectUri()))
                .queryParam("client_id", config.getClientId())
                //文档p5   随机值，若为门户发起，可自定义随机一个字符串，若为IDP发起，也是随机一个值，但会带上_idp后缀
                .queryParam("client_secret", config.getClientSecret()).build();
        System.out.println("获取token url" + url);

        //文档未见说明，调试时候酌情添加header
        try {
            String response = new HttpUtils(config.getHttpConfig()).post(url).getBody();
            System.out.println("获取token" + response);
            JSONObject jsonObject = JSONObject.parseObject(response);
            this.checkResponse(jsonObject);
            return AuthToken.builder()
                    .accessToken(jsonObject.getString("access_token"))
                    .tokenType(jsonObject.getString("token_type"))
                    .expireIn(jsonObject.getIntValue("expires_in"))
                    .refreshToken(jsonObject.getString("refresh_token"))
                    .build();
        } catch (Exception e) {
            System.err.println("获取token失败，URL: " + url + "，错误信息：" + e.getMessage());
            e.printStackTrace();
            throw new AuthException("获取token失败：" + e.getMessage());
        }
    }

    @Override
    public AuthUser getUserInfo(AuthToken authToken) {
        String url = UrlBuilder.fromBaseUrl(source.userInfo().replace("{IDaaS_server}", this.idaasServer)).queryParam("access_token", authToken.getAccessToken()).build();
        String userInfo = new HttpUtils(config.getHttpConfig()).get(url).getBody();
        System.out.println("获取userInfo" + userInfo);
        JSONObject object = JSONObject.parseObject(userInfo);
        this.checkResponse(object);

        JSONObject data = object.getJSONObject("data");
        return AuthUser.builder()
                .rawUserInfo(object)
                .uuid(data.getString("username"))
                .username(data.getString("nickname"))
                .nickname(data.getString("nickname"))
                .email(data.getString("email"))
                .company(data.getString("ou_name"))
                .remark(data.getString("phone_number"))
                .gender(AuthUserGender.getRealGender(data.getString("sex")))
                .token(authToken)
                .source(source.toString())
                .build();
    }

    @Override
    public String authorize(String state) {
        String url = UrlBuilder.fromBaseUrl(source.authorize())
                .queryParam("response_type", "code")
                .queryParam("scope", "read")
                .queryParam("client_id", config.getClientId())
                .queryParam("redirect_uri", config.getRedirectUri())
                //文档p5   随机值，若为门户发起，可自定义随机一个字符串，若为IDP发起，也是随机一个值，但会带上_idp后缀
                .queryParam("state", getRealState(state))
                .build();

        url = url.replace("{IDaaS_server}", this.idaasServer);
        System.out.println("获取authorize url" + url);
        return url;
    }


    /**
     * 检查响应内容是否正确
     *
     * @param object 请求响应内容
     */
    private void checkResponse(JSONObject object) {
        if (object.containsKey("error")) {
            throw new AuthException(object.getString("error_description"));
        }
    }
}
