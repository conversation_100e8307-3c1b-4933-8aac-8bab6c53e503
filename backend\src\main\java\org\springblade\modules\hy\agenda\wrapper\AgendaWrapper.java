/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.agenda.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.hy.agenda.pojo.entity.AgendaEntity;
import org.springblade.modules.hy.agenda.pojo.vo.AgendaVO;
import java.time.LocalDate;
import java.util.Map;
import java.util.Objects;

/**
 * 会议议程表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
public class AgendaWrapper extends BaseEntityWrapper<AgendaEntity, AgendaVO>  {

	public static AgendaWrapper build() {
		return new AgendaWrapper();
 	}

	@Override
	public AgendaVO entityVO(AgendaEntity agenda) {
		AgendaVO agendaVO = Objects.requireNonNull(BeanUtil.copyProperties(agenda, AgendaVO.class));

		//User createUser = UserCache.getUser(agenda.getCreateUser());
		//User updateUser = UserCache.getUser(agenda.getUpdateUser());
		//agendaVO.setCreateUserName(createUser.getName());
		//agendaVO.setUpdateUserName(updateUser.getName());

		return agendaVO;
	}

	/**
	 * 查询条件处理
	 */
	public void agendaQuery(Map<String, Object> agenda) {
		// 处理日期查询参数的类型转换
		String dateKey = "date_equal";
		if (Func.isNotEmpty(agenda.get(dateKey))) {
			String dateStr = Func.toStr(agenda.get(dateKey));
			try {
				// 将字符串日期转换为LocalDate类型
				LocalDate date = LocalDate.parse(dateStr);
				agenda.put(dateKey, date);
			} catch (Exception e) {
				// 如果转换失败，移除该查询条件
				agenda.remove(dateKey);
			}
		}
	}

}
