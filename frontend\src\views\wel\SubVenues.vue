<template>
  <div class="page-container">
    <main class="page-content">
      <div class="list-container">
        <div class="form-header">
          <i class="fas fa-video"></i>
          <h2>{{ mainTitle || "云展厅列表" }}</h2>
          <p>{{ subTitle || "多会场同步，全方位参与" }}</p>
        </div>

        <!-- 加载状态 -->
        <LoadingIndicator
          :show="isLoading"
          text="正在加载云展厅信息..."
          size="small"
        />

        <!-- 错误提示 -->
        <ErrorMessage
          :show="hasError && !isLoading"
          type="warning"
          :message="errorMessage"
          :show-retry="true"
          :retrying="isLoading"
          @retry="loadVenuesData"
          @close="clearError"
        />

        <!-- 云展厅列表滚动容器 -->
        <div class="venues-scroll-container">
          <div class="venue-list">
            <!-- 固定云展厅 -->
            <div class="venue-item">
              <div class="venue-header">
                <div class="venue-number">A</div>
                <div class="venue-info">
                  <h3>物流配送中心分会场</h3>
                  <div class="venue-meta">
                    <span class="venue-location">
                      <i class="fas fa-map-marker-alt"></i>
                      广州市荔湾区龙溪大道408号
                    </span>
                    <span class="venue-time">
                      广东烟草广州市有限公司配送中心，位于中国的南大门羊城广州，已经为客户提供了24年优质的服务，中心主要提供卷烟物流存储、分装、配送等业务，欢迎各界朋友莅临参观、指导和业务洽谈。
                    </span>
                  </div>
                </div>
                <div class="venue-status active">
                  <i class="fas fa-circle"></i>
                  进行中
                </div>
              </div>

              <div class="venue-actions">
                <button class="action-btn primary" @click="joinVenue('A')">
                  <i class="fas fa-play" />
                  进入会场
                </button>
              </div>
            </div>

            <div class="venue-item">
              <div class="venue-header">
                <div class="venue-number">B</div>
                <div class="venue-info">
                  <h3>穗烟印象工作室分会场</h3>
                  <div class="venue-meta">
                    <span class="venue-location">
                      <i class="fas fa-map-marker-alt"></i>
                      广州市天河区林和西横路186号广州烟草大厦18层
                    </span>
                    <span class="venue-time"
                      >广州烟草"穗烟映像"新媒体工作室是集创意策划、内容生产、品牌推广于一体的融媒体平台。工作室拥有专业团队32人，配备先进拍摄制作设备，已创作《香云纱》等行业获奖作品，短视频总点赞量超6万。创新研发设备智能管理系统获行业QC二等奖，打造"二十狮"文创IP及周边产品。同时孵化10家基层融媒体工作室，形成品牌矩阵。未来将持续输出优质内容，讲好烟草故事，服务高质量发展。，位于中国的南大门羊城广州，已经为客户提供了24年优质的服务，中心主要提供卷烟物流存储、分装、配送等业务，欢迎各界朋友莅临参观、指导和业务洽谈。
                    </span>
                  </div>
                </div>
                <div class="venue-status preparing">
                  <i class="fas fa-circle"></i>
                  准备中
                </div>
              </div>

              <div class="venue-actions">
                <button class="action-btn disabled" disabled>
                  <i class="fas fa-clock"></i>
                  未开始
                </button>
              </div>
            </div>

            <div class="venue-item">
              <div class="venue-header">
                <div class="venue-number">C</div>
                <div class="venue-info">
                  <h3>人工智能工作室分会场</h3>
                  <div class="venue-meta">
                    <span class="venue-location">
                      <i class="fas fa-map-marker-alt"></i>
                      广州市天河区林和西横路186号广州烟草大厦21层
                    </span>
                    <span class="venue-location">
                      人工智能创新工作室将“走在前列·智赢未来”的理念融入空间设计，数字大屏实时跃动全链数据，前沿AI产品零距离互动，沉浸式成果体验区让智慧企管触手可及，全景感受AI赋能业务的澎湃动能。
                    </span>
                  </div>
                </div>
                <div class="venue-status finished">
                  <i class="fas fa-check-circle"></i>
                  已结束
                </div>
              </div>

              <div class="venue-actions">
                <button class="action-btn secondary" @click="viewReplay('C')">
                  <i class="fas fa-redo" />
                  观看回放
                </button>
              </div>
            </div>

            <div class="venue-item">
              <div class="venue-header">
                <div class="venue-number">D</div>
                <div class="venue-info">
                  <h3>企业文化展馆分会场</h3>
                  <div class="venue-meta">
                    <span class="venue-location">
                      <i class="fas fa-map-marker-alt"></i>
                      广州市天河区林和西横路186号广州烟草大厦16层
                    </span>
                    <span class="venue-location">
                      这里以广州烟草的企业文化核心理念“实创精进
                      善和乐美”为主题，分为文化传承、发展足迹、党建引领、人才高地、高质量发展这五个板块；
                      企业文化中心既是广州烟草推动人才强烟、数字强烟、文化强烟的精神堡垒与实践载体，更紧扣广州市局(公司)“高质量发展示范区”定位，为全省系统文化赋能管理升级提供了可借鉴的鲜活样本。
                    </span>
                  </div>
                </div>
                <div class="venue-status finished">
                  <i class="fas fa-check-circle"></i>
                  已结束
                </div>
              </div>

              <div class="venue-actions">
                <button class="action-btn secondary" @click="viewReplay('C')">
                  <i class="fas fa-redo" />
                  观看回放
                </button>
              </div>
            </div> -->

            <!-- 动态渲染其他云展厅 -->
            <div
              v-for="venue in otherVenues"
              :key="venue.id"
              class="venue-item"
            >
              <div class="venue-header">
                <div class="venue-number">{{ venue.number }}</div>
                <div class="venue-info">
                  <h3>{{ venue.name }}</h3>
                  <div class="venue-meta">
                    <span class="venue-location">
                      <i class="fas fa-map-marker-alt"></i>
                      {{ venue.location }}
                    </span>
                    <span class="venue-time">
                      {{ venue.time }}
                    </span>
                  </div>
                </div>
                <div class="venue-status" :class="venue.status">
                  <i :class="getStatusIcon(venue.status)"></i>
                  {{ getStatusText(venue.status) }}
                </div>
              </div>

              <div class="venue-actions">
                <button
                    class="action-btn"
                    :class="getActionButtonClass(venue.status)"
                    @click="handleVenueAction(venue)"
                    :disabled="venue.status === 'preparing'"
                >
                  <i :class="getActionIcon(venue.status)"></i>
                  {{ getActionText(venue.status) }}
                </button>
                <button class="action-btn secondary" @click="viewSchedule(venue.number)">
                  <i class="fas fa-calendar"></i>
                  查看议程
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="venue-stats">
          <div class="stat-item">
            <i class="fas fa-building"></i>
            <span
              >总会场数：<strong>{{ totalVenues }}</strong></span
            >
          </div>
          <div class="stat-item">
            <i class="fas fa-users"></i>
            <span
              >参与人数：<strong>{{ totalCapacity }}</strong
              >人</span
            >
          </div>
          <!-- <div class="stat-item">
            <i class="fas fa-database"></i>
            <span
              >数据源：<strong>{{
                dataSource === "api"
                  ? "API"
                  : dataSource === "fallback"
                  ? "默认"
                  : "未知"
              }}</strong></span
            >
          </div> -->
        </div>
      </div>
    </main>
    <!-- 底部导航栏占位符 -->
    <div class="bottom-nav-placeholder"></div>

    <!-- PDF预览模态框 -->
    <PdfModal
      :visible="pdfModalVisible"
      :pdf-url="currentPdfUrl"
      :title="currentPdfTitle"
      @close="closePdfModal"
    />

    <!-- 视频播放模态框 -->
    <div v-if="videoModalVisible" class="video-modal" @click="closeVideoModal">
      <div class="video-modal-overlay">
        <div class="video-modal-content" @click.stop>
          <div class="video-modal-header">
            <h3>{{ currentVideoTitle }} - 视频播放</h3>
            <button class="video-modal-close" @click="closeVideoModal">
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div class="video-modal-body">
            <video
              v-if="currentVideoUrl"
              controls
              autoplay
              style="width: 100%; height: 400px;"
              @loadstart="onVideoLoadStart"
              @error="onVideoError"
            >
              <source :src="currentVideoUrl" type="video/mp4">
              您的浏览器不支持视频播放。
            </video>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getList } from '@/api/subvenue/subVenue';
import { dataTransformers } from '@/utils/apiHelper';
import { getDictionary } from '@/api/system/dictbiz';
import LoadingIndicator from '@/components/LoadingIndicator.vue';
import ErrorMessage from '@/components/ErrorMessage.vue';
import PdfModal from '@/components/PdfModal.vue';
import { Soccer } from "@element-plus/icons-vue";

export default {
  name: "SubVenues",
  components: {
    Soccer,
    LoadingIndicator,
    ErrorMessage,
    PdfModal

  },
  data() {
    return {
      mainTitle: "",
      subTitle: "",
      venues: [],
      otherVenues: [], // 动态渲染的其他分会场
      dataSource: 'unknown',
      responseTime: 0,
      isLoading: false,
      hasError: false,
      errorMessage: '',
      // PDF预览相关状态
      pdfModalVisible: false,
      currentPdfUrl: '',
      currentPdfTitle: '',
      // 视频播放相关状态
      videoModalVisible: false,
      currentVideoUrl: '',
      currentVideoTitle: '',
      defaultVenuesData: [
        {
          id: 4,
          number: 'D',
          name: '产业发展论坛',
          location: '会议中心D厅',
          time: '15:00-18:00',
          capacity: 80,
          currentAttendees: 65,
          status: 'preparing',
          currentTopic: '产业升级与发展趋势',
          speaker: '陈专家',
          speakerTitle: '产业发展顾问',
          videoUrl: '', // 视频URL，需要从API获取
          pdfUrl: '' // PDF文档URL，需要从API获取
        },
        {
          id: 5,
          number: 'E',
          name: '技术创新沙龙',
          location: '会议中心E厅',
          time: '10:00-12:00',
          capacity: 60,
          currentAttendees: 45,
          status: 'finished',
          currentTopic: '新技术应用与实践',
          speaker: '刘工程师',
          speakerTitle: '技术创新专家',
          videoUrl: '', // 视频URL，需要从API获取
          pdfUrl: '' // PDF文档URL，需要从API获取
        }
      ]
    }
  },
  computed: {
    totalVenues() {
      return 3 + this.otherVenues.length; // 3个固定分会场 + 动态分会场
    },
    totalCapacity() {
      const fixedCapacity = 150 + 100 + 120; // A厅150 + B厅100 + C厅120
      const dynamicCapacity = this.otherVenues.reduce(
        (sum, venue) => sum + venue.capacity,
        0
      );
      return fixedCapacity + dynamicCapacity;
    }
  },
  async mounted() {
    // 初始化默认数据
    this.otherVenues = [...this.defaultVenuesData];

    // 加载API数据
    await this.loadVenuesData();
    await this.loadData();
  },
  methods: {
    async loadData() {
      try {
        const response = await getDictionary({
          code: "hy_sub_venue",
        });
        if (response && response.data && response.data.success) {
          const dictData = response.data.data;
          if (dictData && Array.isArray(dictData) && dictData.length > 0) {
            this.mainTitle = dictData.find(
              (item) => item.dictValue === "主标题"
            )?.dictKey;
            this.subTitle = dictData.find(
              (item) => item.dictValue === "副标题"
            )?.dictKey;
          }
        } else {
          throw new Error("API响应格式不正确");
        }
      } catch (error) {
        console.error("加载标题数据失败:", error);
      }
    },

    clearError() {
      this.hasError = false;
      this.errorMessage = "";
    },

    /**
     * 加载云展厅数据
     */
    async loadVenuesData() {
      const startTime = Date.now();
      this.isLoading = true;

      try {
        const response = await getList(1, 20, {});
        console.log("云展厅API响应:", response);

        if (response && response.data && response.data.success) {
          const transformedData = dataTransformers.subVenues(response.data);

          if (transformedData && transformedData.length > 0) {
            this.otherVenues = transformedData;
            this.dataSource = "api";
          } else {
            this.dataSource = "fallback";
          }
          this.hasError = false;
          this.errorMessage = "";
        } else {
          throw new Error("API响应格式不正确");
        }

        this.responseTime = Date.now() - startTime;
      } catch (error) {
        console.error("加载云展厅数据失败:", error);
        this.dataSource = "fallback";
        this.hasError = true;
        this.errorMessage = error.message || "数据加载失败，使用默认数据";
        this.responseTime = Date.now() - startTime;
      } finally {
        this.isLoading = false;
      }
    },

    // 状态文本映射
    getStatusText(status) {
      const statusMap = {
        active: "进行中",
        preparing: "准备中",
        finished: "已结束",
        cancelled: "已取消",
      };
      return statusMap[status] || "未知";
    },

    // 状态图标映射
    getStatusIcon(status) {
      const iconMap = {
        active: "fas fa-circle",
        preparing: "fas fa-circle",
        finished: "fas fa-check-circle",
        cancelled: "fas fa-times-circle",
      };
      return iconMap[status] || "fas fa-circle";
    },

    // 直播状态图标映射
    getStreamIcon(status) {
      const iconMap = {
        active: "fas fa-video",
        preparing: "fas fa-pause",
        finished: "fas fa-check",
        cancelled: "fas fa-times",
      };
      return iconMap[status] || "fas fa-pause";
    },

    // 直播状态文本映射
    getStreamStatus(status) {
      const statusMap = {
        active: "直播中",
        preparing: "待开始",
        finished: "已完成",
        cancelled: "已取消",
      };
      return statusMap[status] || "未知";
    },

    // 按钮样式映射
    getActionButtonClass(status) {
      const classMap = {
        active: "primary",
        preparing: "disabled",
        finished: "secondary",
        cancelled: "disabled",
      };
      return classMap[status] || "secondary";
    },

    // 按钮图标映射
    getActionIcon(status) {
      const iconMap = {
        active: "fas fa-sign-in-alt",
        preparing: "fas fa-clock",
        finished: "fas fa-play",
        cancelled: "fas fa-times",
      };
      return iconMap[status] || "fas fa-info";
    },

    // 按钮文本映射
    getActionText(status) {
      const textMap = {
        active: "进入云展厅",
        preparing: "未开始",
        finished: "观看回放",
        cancelled: "已取消",
      };
      return textMap[status] || "查看详情";
    },

    // 进入分会场
    joinVenue(venueNumber) {
      if (this.$message) {
        this.$message.success(`正在进入云展厅${venueNumber}`);
      } else {
        alert(`正在进入云展厅${venueNumber}\n（演示功能）`);
      }
    },

    /**
     * 查看议程 - PDF预览
     */
    viewSchedule(venueNumber) {
      const venue = this.otherVenues.find(v => v.number === venueNumber);
      if (venue && venue.pdfUrl) {
        // 使用新的PDF预览组件
        this.currentPdfUrl = venue.pdfUrl;
        this.currentPdfTitle = `${venue.name} - 议程预览`;
        this.pdfModalVisible = true;
      } else {
        if (this.$message) {
          this.$message.warning(`分会场${venueNumber}暂无议程文档`);
        } else {
          alert(`分会场${venueNumber}暂无议程文档`);
        }
      }
    },

    /**
     * 关闭PDF预览模态框
     */
    closePdfModal() {
      this.pdfModalVisible = false;
      this.currentPdfUrl = '';
      this.currentPdfTitle = '';
    },

    /**
     * 关闭视频播放模态框
     */
    closeVideoModal() {
      this.videoModalVisible = false;
      this.currentVideoUrl = '';
      this.currentVideoTitle = '';
    },

    /**
     * 视频加载开始事件
     */
    onVideoLoadStart() {
      console.log('视频开始加载');
    },

    /**
     * 视频加载错误事件
     */
    onVideoError(event) {
      console.error('视频加载失败:', event);
      if (this.$message) {
        this.$message.error('视频加载失败，请稍后重试');
      } else {
        alert('视频加载失败，请稍后重试');
      }
    },

    // 观看回放
    viewReplay(venueNumber) {
      const venue = this.otherVenues.find(v => v.number === venueNumber);
      if (venue && venue.videoUrl) {
        // 打开视频播放
        this.openVideoPlayer(venue.videoUrl, venue.name);
      } else {
        if (this.$message) {
          this.$message.warning(`云展厅${venueNumber}暂无回放视频`);
        } else {
          alert(`云展厅${venueNumber}暂无回放视频`);
        }
      }
    },

    // 处理云展厅操作
    handleVenueAction(venue) {
      switch (venue.status) {
        case "active":
          this.joinVenue(venue.number);
          break;
        case "finished":
          this.viewReplay(venue.number);
          break;
        case 'preparing':
          // 准备中不允许操作
          break;
        default:
          // 默认情况下查看详情（视频播放）
          if (venue.videoUrl) {
            this.openVideoPlayer(venue.videoUrl, venue.name);
          } else {
            if (this.$message) {
              this.$message.info(`云展厅${venue.number}暂无视频内容`);
            } else {
              alert(`云展厅${venue.number}暂无视频内容`);
            }
          }
      }
    },

    /**
     * 打开视频播放器 - Vue响应式方式
     */
    openVideoPlayer(videoUrl, venueName) {
      if (!videoUrl) {
        if (this.$message) {
          this.$message.warning('暂无视频内容');
        } else {
          alert('暂无视频内容');
        }
        return;
      }

      // 使用Vue的响应式数据来控制模态框显示
      this.currentVideoUrl = videoUrl;
      this.currentVideoTitle = venueName;
      this.videoModalVisible = true;
    },




  }
}
</script>

<style scoped>
/* 页面通用样式*/
.page-container {
  width: 100%;
  min-height: 85vh;
  background-size: cover;
  position: relative;
  margin: 0 auto;
  box-sizing: border-box;
}

.page-content {
  margin-top: 15px;
}

/* 容器样式 - 磨砂玻璃效果 */
.list-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  border-radius: 15px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.05),
    0 10px 25px rgba(0, 0, 0, 0.3);
  animation: slideInUp 0.6s ease forwards;
  max-width: 1400px;
  margin: 0 auto;
  max-height: 85vh;
}

/* 标题区域样式 */
.form-header {
  text-align: center;
  margin-bottom: 25px;
  color: #ffffff;
}

.form-header i {
  font-size: 36px;
  color: #07d3f0;
  margin-bottom: 10px;
  text-shadow: 0 0 15px rgba(7, 211, 240, 0.6);
}

.form-header h2 {
  color: #ffffff;
  font-size: 24px;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.form-header p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  padding: 0 10px;
}

/* 云展厅列表滚动容器 */
.venues-scroll-container {
  max-height: calc(85vh - 250px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-right: 5px;
  margin-bottom: 25px;
}

/* 滚动条美化 */
.venues-scroll-container::-webkit-scrollbar {
  width: 5px;
}

.venues-scroll-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.venues-scroll-container::-webkit-scrollbar-thumb {
  background: rgba(7, 211, 240, 0.5);
  border-radius: 10px;
}

.venues-scroll-container::-webkit-scrollbar-thumb:hover {
  background: rgba(7, 211, 240, 0.8);
}

/* 云展厅列表样式 */
.venue-list {
  margin: 20px 0;
}

/* 列表项样式 */
.venue-item {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid rgba(7, 211, 240, 0.7);
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease forwards;
  position: relative;
  overflow: hidden;
}

/* 卡片悬停效果 */
.venue-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.venue-item:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.venue-item:hover {
  transform: translateX(3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.09);
}

/* 云展厅头部样式 */
.venue-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.venue-number {
  width: 50px;
  height: 50px;
  background: linear-gradient(
    135deg,
    rgba(7, 211, 240, 0.7),
    rgba(7, 211, 240, 0.4)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  font-weight: bold;
  margin-right: 15px;
  box-shadow: 0 4px 15px rgba(7, 211, 240, 0.3);
}

.venue-info {
  flex: 1;
}

.venue-info h3 {
  color: #ffffff;
  font-size: 18px;
  margin-bottom: 8px;
  font-weight: 600;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.venue-meta {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.venue-location,
.venue-time {
  display: flex;
  align-items: center;
  gap: 5px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
}

.venue-location i,
.venue-time i {
  color: #07d3f0;
  font-size: 12px;
}

/* 状态标识样式 */
.venue-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  color: white;
}

.venue-status.active {
  background: rgba(160, 246, 180, 0.2);
  color: #92f6a9;
  border: 1px solid rgba(76, 170, 97, 0.3);
}

.venue-status.preparing {
  background: rgba(252, 227, 138, 0.2); /* 更柔和的黄色背景 */
  color: #fad43d; /* 稍暗的金色文字，提升可读性 */
  border: 1px solid rgba(207, 174, 71, 0.3); /* 协调的边框色 */
}

.venue-status.finished {
  background: rgba(204, 215, 225, 0.5);
  color: #6c757d;
  border: 1px solid rgba(108, 117, 125, 0.3);
}

.venue-status.cancelled {
  background: rgba(220, 53, 69, 0.2);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.venue-status i {
  font-size: 10px;
}

/* 云展厅内容区域样式 */
.venue-content {
  margin-bottom: 20px;
}

.session-info {
  margin-bottom: 15px;
}

.session-info h4 {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  margin-bottom: 10px;
  font-weight: 600;
}

.speaker-info {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(7, 211, 240, 0.05);
  padding: 12px;
  border-radius: 10px;
}

.speaker-avatar {
  width: 40px;
  height: 40px;
  background: rgba(7, 211, 240, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.speaker-avatar i {
  font-size: 18px;
  color: #07d3f0;
}

.speaker-details {
  flex: 1;
}

.speaker-name {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 600;
  display: block;
  margin-bottom: 2px;
}

.speaker-title {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

/* 统计信息行样式 */
.venue-stats-row {
  display: flex;
  gap: 20px;
  margin-top: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
}

.stat-item i {
  color: #07d3f0;
  font-size: 14px;
}

/* 操作按钮样式 */
.venue-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.action-btn {
  background: rgba(7, 211, 240, 0.2);
  color: #07d3f0;
  border: 1px solid rgba(7, 211, 240, 0.3);
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
  flex: 1;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.action-btn:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.action-btn.primary {
  background: linear-gradient(
    135deg,
    rgba(7, 211, 240, 0.3),
    rgba(7, 211, 240, 0.15)
  );
  color: white;
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.08);
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.action-btn.disabled {
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.05);
  cursor: not-allowed;
}

.action-btn:hover:not(.disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(7, 211, 240, 0.3);
}

.action-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.12);
}

.action-btn i {
  font-size: 10px;
}

/* 统计信息样式 */
.venue-stats {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 15px;
  margin-top: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.venue-stats .stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
}

.venue-stats .stat-item i {
  color: #07d3f0;
  font-size: 16px;
}

.venue-stats .stat-item strong {
  color: #07d3f0;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .list-container {
    padding: 15px;
  }

  .venue-item {
    padding: 15px;
  }

  .venue-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .venue-number {
    width: 40px;
    height: 40px;
    font-size: 16px;
    margin-right: 0;
  }

  .venue-meta {
    flex-direction: column;
    gap: 8px;
  }

  .venue-stats-row {
    flex-direction: column;
    gap: 10px;
  }

  .venue-actions {
    flex-direction: column;
  }

  .action-btn {
    padding: 10px 16px;
    font-size: 13px;
  }

  .speaker-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* 交错动画 */
.venue-item:nth-child(1) { animation-delay: 0.1s; }
.venue-item:nth-child(2) { animation-delay: 0.2s; }
.venue-item:nth-child(3) { animation-delay: 0.3s; }
.venue-item:nth-child(4) { animation-delay: 0.4s; }
.venue-item:nth-child(5) { animation-delay: 0.5s; }

/* 视频弹窗样式 */
.video-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.video-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
}

.video-modal-content {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 90vw;
  max-height: 90vh;
  width: 800px;
  overflow: hidden;
  animation: slideInScale 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.video-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 25px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.video-modal-header h3 {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.video-modal-close {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.8);
}

.video-modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.video-modal-close i {
  font-size: 16px;
}

.video-modal-body {
  padding: 25px;
  background: rgba(0, 0, 0, 0.2);
}

.video-modal-body video {
  width: 100%;
  height: 400px;
  border-radius: 10px;
  background: #000;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* 视频弹窗动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(30px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 视频弹窗响应式设计 */
@media (max-width: 768px) {
  .video-modal-content {
    width: 95vw;
    max-width: none;
  }

  .video-modal-header {
    padding: 15px 20px;
  }

  .video-modal-header h3 {
    font-size: 16px;
  }

  .video-modal-close {
    width: 35px;
    height: 35px;
  }

  .video-modal-close i {
    font-size: 14px;
  }

  .video-modal-body {
    padding: 20px;
  }

  .video-modal-body video {
    height: 250px;
  }
}

@media (max-width: 480px) {
  .video-modal-overlay {
    padding: 10px;
  }

  .video-modal-content {
    width: 100%;
  }

  .video-modal-header {
    padding: 12px 15px;
  }

  .video-modal-header h3 {
    font-size: 14px;
  }

  .video-modal-body {
    padding: 15px;
  }

  .video-modal-body video {
    height: 200px;
  }
}

/* 确保弹窗在所有元素之上 */
.video-modal,
.pdf-modal {
  z-index: 10000 !important;
}

/* 底部导航占位符 */
.bottom-nav-placeholder {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 10vh;
  z-index: 10;
  pointer-events: none;
}
</style>
