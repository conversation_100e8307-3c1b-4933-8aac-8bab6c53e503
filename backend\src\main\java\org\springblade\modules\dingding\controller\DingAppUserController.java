package org.springblade.modules.dingding.controller;

import cn.hutool.core.bean.BeanUtil;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.support.Kv;
import org.springblade.modules.dingding.service.IDingAppUserService;
import org.springblade.modules.dingding.utils.DdConfigSign;
import org.springblade.modules.system.pojo.entity.User;
import org.springblade.modules.system.pojo.vo.UserVO;
import org.springblade.modules.system.service.IUserService;
import org.springblade.modules.system.wrapper.UserWrapper;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-22 10:58
 */
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/dingapp/user")
public class DingAppUserController {

    private IDingAppUserService dingAppUserService;
    private IUserService userService;

    @PostMapping("/login")
    public Kv login(@Valid @RequestBody Map<String, Object> dataMap) throws Exception {
        String authCode = (String) dataMap.get("authCode");
        Kv login = dingAppUserService.login(authCode);
        return login;
    }


    @GetMapping("/getDingTalkApiParam")
    public Kv getDingTalkApiParam(@RequestParam(name = "url")String url,
                                      @RequestParam(name = "nonceStr")String nonceStr,
                                      @RequestParam(name = "timeStamp")Long timeStamp){
        log.info("钉钉签名请求参数: url={}, nonceStr={}, timeStamp={}", url, nonceStr, timeStamp);
        Kv kv=Kv.create();
        try{
            String ticket=dingAppUserService.getJsapiTicket();
            log.info("获取到的jsapi_ticket: {}", ticket);
            String signature= DdConfigSign.sign(ticket,nonceStr,timeStamp,url);
            log.info("生成的签名: {}", signature);
            kv.put("code",200);
            kv.put("signature",signature);
        }catch (Exception e){
            log.error("钉钉签名生成失败: ", e);
            kv.put("code",500);
            kv.put("signature",e.getMessage());
        }
        return kv;
    }

    //执法人员列表
//    @GetMapping("/getLawList")
//    public R<List<UserVO>> listLawByName(@RequestParam(name = "userName",required = false) String userName) {
//        List<UserVO> userVOList = userService.listLawByName(userName);
//        //额外存一下userid
//        Map<String, String> userVOMap = userVOList.stream()
//                .filter(Objects::nonNull)  // 过滤掉null元素
//                .filter(userVO -> userVO.getId() != null)  // 过滤掉id为null的元素
//                .collect(Collectors.toMap(
//                        userVO -> userVO.getId().toString(),
//                        userVO -> userVO.getUserid() != null ? userVO.getUserid() : "",  // 处理userId为null的情况
//                        (v1, v2) -> v1  // 处理重复key的情况
//                ));
//
//        List<User> userList = userVOList.stream()
//        .map(vo -> {
//            User user = new User();
//            BeanUtil.copyProperties(vo, user);
//            return user;
//        })
//        .collect(Collectors.toList());
//
//        List<UserVO> userVOS = UserWrapper.build().listVO(userList);
//        userVOS.forEach(vo -> {
//            String s = userVOMap.get(vo.getId().toString());
//            vo.setUserid(s);
//        });
//
//
//        return R.data(userVOS);
//    }
}
