const getters = {
  tag: state => state.tags.tag,
  language: state => state.common.language,
  setting: state => state.common.setting,
  userInfo: state => state.user.userInfo,
  colorName: state => state.common.colorName,
  themeName: state => state.common.themeName,
  isMacOs: (state, getters) => getters.themeName === 'mac-os',
  isRefresh: state => state.common.isRefresh,
  isSearch: state => state.common.isSearch,
  isHorizontal: state => state.common.setting.sidebar === 'horizontal',
  isCollapse: state => state.common.isCollapse,
  isLock: state => state.common.isLock,
  isFullScren: state => state.common.isFullScren,
  isMenu: state => state.common.isMenu,
  lockPasswd: state => state.common.lockPasswd,
  tagList: state => state.tags.tagList,
  tagsKeep: (state, getters) => {
    return getters.tagList
      .filter(ele => {
        return (ele.meta || {}).keepAlive;
      })
      .map(ele => ele.fullPath);
  },
  tagWel: state => state.tags.tagWel,
  token: state => state.user.token,
  roles: state => state.user.roles,
  permission: state => state.user.permission,
  menuId: state => state.user.menuId,
  menu: state => state.user.menu,
  menuAll: state => state.user.menuAll,
  logsList: state => state.logs.logsList,
  logsLen: state => state.logs.logsList.length || 0,
  logsFlag: (state, getters) => getters.logsLen === 0,
  flowRoutes: state => state.dict.flowRoutes,
  // IDAAS移动端相关getters
  redirectPath: state => state.user.redirectPath,
  // 移动端登录状态检查（使用统一的token和userInfo）
  isMobileLoggedIn: state => {
    // console.log('=== isMobileLoggedIn getter 调试信息 ===');
    // console.log('state.user.token:', state.user.token);
    // console.log('state.user.userInfo:', state.user.userInfo);

    // 检查token
    if (!state.user.token || state.user.token.length === 0) {
      console.log('Token 检查失败');
      return false;
    }

    // 检查用户信息
    const userInfo = state.user.userInfo;
    if (!userInfo || !userInfo.user_id) {
      console.log('UserInfo 检查失败，userInfo:', userInfo);
      return false;
    }

    console.log('移动端登录检查通过');
    return true;
  },
};
export default getters;
