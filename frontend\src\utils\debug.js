/**
 * 调试工具模块
 * 
 * 这个文件提供了应用的调试功能，包括:
 * 1. vConsole - 移动端调试控制台
 * 2. 钉钉H5远程调试功能
 * 
 * 使用方法:
 * 1. 在main.js中导入并初始化此模块
 * 2. 通过环境变量控制是否显示调试控制台
 */

import VConsole from 'vconsole';

// 是否启用调试模式
let isDebugEnabled = false;
let vConsoleInstance = null;

/**
 * 初始化调试模式
 * @param {boolean} forceEnable - 是否强制启用调试模式，不考虑环境
 * @returns {boolean} - 是否成功启用调试模式
 */
export const initDebugMode = (forceEnable = false) => {
  // 检查是否为开发环境或强制启用
  const isDev = import.meta.env.DEV;
  const showVConsole = import.meta.env.VITE_SHOW_VCONSOLE === 'true';
  isDebugEnabled = isDev || forceEnable || showVConsole;
  console.log(123)
  if (isDebugEnabled) {
    // 初始化 vConsole
    if (!vConsoleInstance) {
      vConsoleInstance = new VConsole({ theme: 'dark' });
    }

    // 添加一些测试日志，确认调试功能正常工作
    console.log('调试模式已启用');
    console.info('可以查看console.info信息');
    console.warn('可以查看console.warn信息');
    console.error('可以查看console.error信息');

    // 输出环境信息
    console.log('当前环境:', isDev ? '开发环境' : '生产环境');
    console.log('应用版本:', import.meta.env.VITE_APP_VERSION || '未定义');
    console.log('钉钉企业ID:', import.meta.env.VITE_DING_CORP_ID);
    console.log('钉钉应用ID:', import.meta.env.VITE_DING_AGENT_ID);
    console.log('vConsole状态:', showVConsole ? '已启用' : '未启用');

    return true;
  } else {
    // 在生产环境中不启用调试模式
    if (vConsoleInstance) {
      vConsoleInstance.destroy();
      vConsoleInstance = null;
    }
    return false;
  }
};

/**
 * 检查调试模式是否已启用
 * @returns {boolean} - 调试模式是否已启用
 */
export const isDebugModeEnabled = () => {
  return isDebugEnabled;
};

/**
 * 添加自定义日志函数，方便在关键位置添加调试信息
 * 只有在调试模式启用时才会输出日志
 * @param {string} message - 日志消息
 * @param {any} data - 附加数据（可选）
 */
export const debugLog = (message, data = null) => {
  if (!isDebugEnabled) return;

  if (data) {
    console.log(`[DEBUG] ${message}:`, data);
  } else {
    console.log(`[DEBUG] ${message}`);
  }
};

/**
 * 添加自定义信息日志函数
 * 只有在调试模式启用时才会输出日志
 * @param {string} message - 日志消息
 * @param {any} data - 附加数据（可选）
 */
export const debugInfo = (message, data = null) => {
  if (!isDebugEnabled) return;

  if (data) {
    console.info(`[INFO] ${message}:`, data);
  } else {
    console.info(`[INFO] ${message}`);
  }
};

/**
 * 添加自定义错误日志函数
 * 错误日志始终会输出，不受调试模式影响
 * @param {string} message - 错误消息
 * @param {Error|any} error - 错误对象（可选）
 */
export const debugError = (message, error = null) => {
  if (error) {
    console.error(`[ERROR] ${message}:`, error);
  } else {
    console.error(`[ERROR] ${message}`);
  }
};

/**
 * 切换vConsole的显示状态
 * @returns {boolean} - 切换后的状态
 */
export const toggleVConsole = () => {
  if (!vConsoleInstance) {
    initDebugMode(true);
    return true;
  }
  
  if (vConsoleInstance.show) {
    vConsoleInstance.hideSwitch();
    return false;
  } else {
    vConsoleInstance.showSwitch();
    return true;
  }
};
