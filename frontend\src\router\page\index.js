import Store from '@/store/';

export default [
  {
    path: '/login',
    name: '登录页',
    component: () =>
      Store.getters.isMacOs ? import('@/mac/login.vue') : import('@/page/login/index.vue'),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/oauth/redirect/:source',
    name: '第三方登录',
    component: () =>
      Store.getters.isMacOs ? import('@/mac/login.vue') : import('@/page/login/index.vue'),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/lock',
    name: '锁屏页',
    component: () =>
      Store.getters.isMacOs ? import('@/mac/lock.vue') : import('@/page/lock/index.vue'),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/404',
    component: () => import(/* webpackChunkName: "page" */ '@/components/error-page/404.vue'),
    name: '404',
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/403',
    component: () => import(/* webpackChunkName: "page" */ '@/components/error-page/403.vue'),
    name: '403',
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/500',
    component: () => import(/* webpackChunkName: "page" */ '@/components/error-page/500.vue'),
    name: '500',
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/',
    name: '主页',
    redirect: '/wel',
  },
  {
    path: '/mobile',
    name: '移动端页面',
    component: () => import('@/views/wel/mobile.vue'),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false, // 保持false，因为我们在路由守卫中单独处理mobile页面
      isMobile: true, // 添加标识，表明这是mobile页面
    },
  },
  {
    path: '/slogin',
    name: 'IDAAS登录页',
    component: () => import('@/views/wel/login/idaaslogin.vue'),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },
  {
    path: '/dinglogin',
    name: '钉钉登录页',
    component: () => import('@/views/wel/login/dinglogin.vue'),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },

  {
    path: '/dev-tools',
    name: '开发工具',
    component: () => import(/* webpackChunkName: "dev" */ '@/views/dev-tools.vue'),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false, // 开发工具页面不需要登录
    },
  },
  {
    path: '/test',
    name: '简单测试',
    component: () => import(/* webpackChunkName: "test" */ '@/views/simple-test.vue'),
    meta: {
      keepAlive: true,
      isTab: false,
      isAuth: false,
    },
  },

];
