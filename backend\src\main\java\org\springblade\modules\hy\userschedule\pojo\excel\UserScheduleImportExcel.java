/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chi<PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.userschedule.pojo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户日程信息表 Excel导入实体类
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class UserScheduleImportExcel implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 用户真实姓名（必填）
	 */
	@ColumnWidth(20)
	@ExcelProperty(value = "用户姓名", index = 0)
	private String userRealName;

	/**
	 * 用户工号
	 */
	@ColumnWidth(20)
	@ExcelProperty(value = "工号", index = 1)
	private String userEmployeeNumber;

	/**
	 * 用户手机号
	 */
	@ColumnWidth(20)
	@ExcelProperty(value = "手机号", index = 2)
	private String userPhone;

	/**
	 * 用户房号
	 */
	@ColumnWidth(20)
	@ExcelProperty(value = "房号", index = 3)
	private String userRoomNumber;

	/**
	 * 用户会议座位号
	 */
	@ColumnWidth(20)
	@ExcelProperty(value = "会议座位号", index = 4)
	private String userMeetingSeatNumber;

	/**
	 * 房号
	 */
	@ColumnWidth(20)
	@ExcelProperty(value = "房号", index = 5)
	private String roomNumber;

	/**
	 * 会议座位号
	 */
	@ColumnWidth(20)
	@ExcelProperty(value = "会议座位号", index = 6)
	private String meetingSeatNumber;

	/**
	 * 日程信息（必填）
	 */
//	@ColumnWidth(50)
//	@ExcelProperty(value = "日程信息", index = 5)
//	private String scheduleContent;

	/**
	 * 用餐信息
	 */
//	@ColumnWidth(40)
//	@ExcelProperty(value = "用餐信息", index = 6)
//	private String diningInfo;

	/**
	 * 住宿信息
	 */
	//	@ColumnWidth(40)
	//	@ExcelProperty(value = "住宿信息", index = 7)
	//	private String accommodationInfo;

}
