<template>
  <div class="page-wrapper">
    <!-- Banner区域 - 由内容决定高度，最大不超过30vh -->
    <div class="banner-container">
      <Banner v-show="!currentComponent" />
    </div>

    <!-- 中间内容区域 - 自动填充剩余空间 -->
    <div class="mobile-container">
      <!-- 议程卡片 - 由内容决定高度，最大不超过20vh -->
      <div class="agenda-container" v-show="!currentComponent">
        <AgendaCard />
      </div>

      <!-- 功能模块区域 - 填充中间容器剩余空间 -->
      <section class="function-grid" v-show="!currentComponent">
        <!-- 功能卡片布局保持不变 -->
        <GlowingCard
          icon-class="fas fa-calendar-alt"
          title="会议议程"
          subtitle="AGENDA"
          color="#00ffe1"
          @navigate="showComponent('agenda')"
        />
        <GlowingCard
          icon-class="fas fa-broadcast-tower"
          title="云直播"
          subtitle="LIVE STREAM"
          color="white"
          @navigate="showComponent('live-stream')"
        />
        <GlowingCard
          icon-class="fas fa-video"
          title="云展厅信息"
          subtitle="SUB VENUES"
          color="white"
          @navigate="showComponent('sub-venues')"
        />
        <GlowingCard
          icon-class="fas fa-file-alt"
          title="会议资料"
          subtitle="MATERIALS"
          color="#00ffe1"
          @navigate="showComponent('materials')"
        />
        <GlowingCard
          icon-class="fas fa-images"
          title="在线相册"
          subtitle="PHOTO"
          color="#00ffe1"
          @navigate="showComponent('photo')"
        />
        <GlowingCard
          icon-class="fas fa-robot"
          title="会务助手"
          subtitle="AI ASSISTANT"
          color="white"
          @navigate="showComponent('ai-chat')"
        />
      </section>

      <!-- 动态组件显示区域 - 填充整个中间容器空间 -->
      <div class="component-container" v-show="currentComponent">
        <component :is="currentComponentName" @navigate="handleNavigate" />
      </div>
    </div>

    <!-- 底部导航 - 严格设置为8vh高度 -->
    <div class="bottom-nav">
      <BottomNav
        :navItems="navItems"
        :activeIndex="activeNavIndex"
        @navigate="showComponent"
      />
    </div>
  </div>
</template>

<script>
// 导入所有需要的组件
import Agenda from "./Agenda.vue";
import LiveStream from "./LiveStream.vue";
import SubVenues from "./SubVenues.vue";
import Materials from "./Materials.vue";
import Photo from "./Photo.vue";
import Guide from "./Guide.vue";
import AiChat from "./AiChat.vue";
import Profile from "./Profile.vue";
import MyDining from "./MyDining.vue";
import MyAccommodation from "./MyAccommodation.vue";
import MySchedule from "./MySchedule.vue";
import CheckIn from "./CheckIn.vue";
import Notices from "@/views/wel/Notices.vue";
import QRCodeManager from "./QRCodeManager.vue";
import { getDictionary } from "@/api/system/dictbiz";
import Banner from "../util/banner.vue";
import GlowingCard from "../util/glowing-card.vue";
import BottomNav from "@/components/bottom-nav/main.vue";
import SlideCard from "@/components/slide-card/main.vue";
import AgendaCard from "@/components/agenda-card/main.vue";
import { useUserStore } from '@/store/mobile/user'

export default {
  name: "MobilePage",
  components: {
    Agenda,
    LiveStream,
    SubVenues,
    Materials,
    Photo,
    Guide,
    AiChat,
    Profile,
    MyDining,
    MyAccommodation,
    MySchedule,
    CheckIn,
    Notices,
    QRCodeManager,
    Banner,
    GlowingCard,
    BottomNav,
    SlideCard,
    AgendaCard,
  },
  setup() {
    // 使用钉钉登录的 Pinia store
    const dingUserStore = useUserStore()
    return {
      dingUserStore
    }
  },
  data() {
    return {
      currentComponent: null, // 当前显示的组件名称
      activeNavIndex: 0, // 当前激活的导航索引
      previousComponent: null, // 上一个组件，用于返回导航
      mainTitle: "", // 主标题，从字典获取
      subTitle: "",
      date: "",
      organizer: "",
      isLoadingDictionary: false, // 防止重复加载字典
      navItems: [
        { label: "主页", iconClass: "fa-house-chimney", routeName: null },
        { label: "签到", iconClass: "fa-qrcode", routeName: "checkin" },
        { label: "我的", iconClass: "fa-user-tie", routeName: "profile" },
      ],
    };
  },
  async mounted() {
    const meta = document.createElement('meta');
    meta.name = 'viewport';
    meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
    document.head.appendChild(meta);
    // 检查钉钉用户登录状态
    this.checkUserAuth()
    // 由于路由守卫已经处理了登录检查，这里只需要加载数据
    await this.loadDictionary();
    this.loadFontAwesome();
    this.currentComponent = null;
    this.activeNavIndex = 0;
  },
  computed: {
    // 使用钉钉登录的用户信息
    userInfo() {
      return this.dingUserStore.userInfo;
    },
    token() {
      return this.dingUserStore.accessToken;
    },
    isLoggedIn() {
      return this.dingUserStore.isLogin;
    },
    currentComponentName() {
      if (!this.currentComponent) return null;

      // 将组件名称映射到实际的组件名
      const componentMap = {
        agenda: "Agenda",
        "live-stream": "LiveStream",
        "sub-venues": "SubVenues",
        materials: "Materials",
        photo: "Photo",
        guide: "Guide",
        "ai-chat": "AiChat",
        profile: "Profile",
        "my-dining": "MyDining",
        "my-accommodation": "MyAccommodation",
        "my-schedule": "MySchedule",
        checkin: "CheckIn",
        notices: 'Notices',
        "QRCodeManager": "QRCodeManager",
      };

      return componentMap[this.currentComponent] || null;
    },
  },
  methods: {
    
    // 检查钉钉登录状态
    checkUserAuth() {
      // 如果钉钉store显示已登录，说明一切正常
      if (this.isLoggedIn && this.userInfo && this.token) {
        return;
      }

      // 如果localStorage有钉钉数据但store没有，尝试初始化store
      const localStorageData = this.checkDingAuthFromLocalStorage();
      if (localStorageData.hasData && !this.isLoggedIn) {
        this.dingUserStore.initUserInfo();
        return;
      }
    },

    // 检查localStorage中的钉钉登录数据
    checkDingAuthFromLocalStorage() {
      try {
        const storedUserInfo = localStorage.getItem('userInfo');
        if (storedUserInfo) {
          const parsedUserInfo = JSON.parse(storedUserInfo);
          const hasToken = !!(parsedUserInfo.access_token);
          const hasUserId = !!(parsedUserInfo.user_id);

          return {
            hasData: hasToken && hasUserId,
            userInfo: parsedUserInfo
          };
        }
        return { hasData: false, userInfo: null };
      } catch (e) {
        return { hasData: false, userInfo: null };
      }
    },
    loadFontAwesome() {
      // 动态引入Font Awesome
      if (!document.getElementById("fa-mobile-page")) {
        const link = document.createElement("link");
        link.id = "fa-mobile-page";
        link.rel = "stylesheet";
        link.href = "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css";
        document.head.appendChild(link);
      }
    },
    async loadDictionary() {
      // 防止重复加载
      if (this.isLoadingDictionary) {
        return;
      }

      this.isLoadingDictionary = true;

      try {
        const response = await getDictionary("wel_config");
        if (response && response.length > 0) {
          response.forEach((item) => {
            if (item.dictKey === "main_title") this.mainTitle = item.dictValue;
            else if (item.dictKey === "sub_title") this.subTitle = item.dictValue;
            else if (item.dictKey === "date") this.date = item.dictValue;
            else if (item.dictKey === "organizer") this.organizer = item.dictValue;
          });
        }
      } catch (error) {
        // 静默处理错误，使用默认配置
      } finally {
        this.isLoadingDictionary = false;
      }
    },
    showComponent(componentName) {
      this.currentComponent = componentName;
      const index = this.navItems.findIndex(item => item.routeName === componentName);
      this.activeNavIndex = index >= 0 ? index : null;
    },
    goBack() {
      if (this.previousComponent) {
        this.currentComponent = this.previousComponent;
        this.previousComponent = null;
      } else {
        this.currentComponent = null;
      }
    },
    handleNavigate(componentName) {
      this.showComponent(componentName);
      const index = this.navItems.findIndex(item => item.routeName === componentName);
      this.activeNavIndex = index >= 0 ? index : null;
    },
    getComponentTitle() {
      const titleMap = {
        agenda: "会议议程",
        "live-stream": "云直播",
        "sub-venues": "云展厅信息",
        materials: "会议资料",
        photo: "在线相册",
        guide: "参会指南",
        "ai-chat": "会务助手",
        profile: "个人中心",
        "my-dining": "我的用餐",
        "my-accommodation": "我的住宿",
        "my-schedule": "我的日程",
        "QRCodeManager": "签到二维码管理",
      };
      return titleMap[this.currentComponent] || "";
    },
  },
};
</script>

<style>
/* 移动端专用样式 - 基于弹性布局 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
html, body {
  overflow: hidden;
  height: 100%;
}

/* 基础响应式设置 */
:root {
  font-size: 16px;
}

@media (max-width: 320px) {
  :root {
    font-size: 14px;
  }
}

@media (min-width: 480px) {
  :root {
    font-size: 18px;
  }
}

/* 页面最外层容器 - 垂直弹性布局 */
.page-wrapper {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: url("img/bg08.jpg") no-repeat center center;
  background-size: cover;
}

/* Banner区域 - 高度由内容决定，最大不超过30vh */
.banner-container {
  max-height: 30vh; /* 限制最大高度 */
  flex-shrink: 0; /* 不压缩 */
  line-height: 0; /* 消除图片间隙 */
  overflow: hidden;
}

/* 中间内容容器 - 自动填充剩余空间 */
.mobile-container {
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
  padding: 3vw;
  box-sizing: border-box;
  flex: 1; /* 填充剩余空间 */
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 议程卡片容器 - 高度由内容决定，最大不超过20vh */
.agenda-container {
  max-height: 20vh; /* 限制最大高度 */
  margin-bottom: 10px;
  flex-shrink: 0; /* 不压缩 */
}

/* 功能网格 - 填充中间容器剩余空间 */
.function-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 2vw;
  justify-content: space-between;
  align-content: flex-start; /* 顶部对齐 */
  flex: 1; /* 填充剩余空间 */
  overflow-y: auto; /* 内容过多时可滚动 */
  padding-bottom: 1vw;
  /* 计算总间隙空间：3行之间有2个垂直间隙，每个2vw */
  --total-vertical-gap: 4vw;
}

/* 功能卡片 - 高度为(function-grid高度 - 6vw)的1/3 */
.function-grid > * {
  flex: 0 0 calc(50% - 2vw);
  /* 计算高度：(功能网格高度 - 6vw) / 3 */
  height: calc((100% - 6vw) / 3);
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* 动态组件容器 - 填充整个中间容器 */
.component-container {
  width: 100%;
  height: 100%;
  overflow-y: auto; /* 内容过多时内部滚动 */
  box-sizing: border-box;
}

/* 底部导航 - 严格设置为8vh高度 */
.bottom-nav {
  height: 8vh; /* 严格设置为8vh */
  min-height: 8vh;
  max-height: 8vh;
  flex-shrink: 0; /* 不压缩 */
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
  position: relative;
  z-index: 10;
}

/* 其他样式保持不变 */
.title-section {
  color: white;
  text-align: center;
  margin-top: 5%;
  margin-bottom: 20%;
  text-shadow: 0 3px 6px rgba(0, 0, 0, 0.5);
}

.main-title {
  font-size: 1.875rem;
  font-weight: bold;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
  background: linear-gradient(90deg, #00d5ff 0%, #ec46ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.conference-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 15px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
}

.main-title, .conference-title {
  font-size: 1.375rem;
  font-weight: bold;
  color: #ffffff;
}

.conference-title, .organizer, .date {
  color: #ffffff;
  opacity: 0.85;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 10vw;
  max-width: 40px;
  height: 10vw;
  max-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.page-header h1 {
  color: white;
  font-size: 1.125rem;
  font-weight: 500;
  margin: 0;
}

.header-placeholder {
  width: 10vw;
  max-width: 40px;
}

.function-card {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(12px);
  border-radius: 18px;
  padding: 5vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.05),
    0 10px 25px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
  z-index: 0;
  color: #fff;
  height: 100%; /* 确保卡片填充父容器高度 */
}

.function-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.function-card:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

.function-card:hover {
  color: #ec46ff;
  background: rgba(255, 255, 255, 0.12);
  transform: translateY(-4px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

.function-card:active {
  transform: translateY(-4px) scale(0.98);
  transition: all 0.1s ease;
}

.card-icon {
  margin-bottom: 8px;
}

.card-icon i {
  font-size: 1.5rem;
  color: #00d5ff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  line-height: 1.5rem;
  text-align: center;
}

.card-text {
  text-align: center;
}

.card-text h3 {
  font-weight: 600;
  margin-bottom: 2px;
  color: #ffffff;
  font-size: 1rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.card-text p {
  color: #ccccff;
  background: none !important;
  -webkit-background-clip: initial !important;
  -webkit-text-fill-color: initial !important;
  font-size: 0.6875rem;
  font-weight: 400;
  letter-spacing: 0.8px;
}

@media (max-width: 360px) {
  .function-card {
    padding: 3vw;
  }

  .card-text h3 {
    font-size: 0.9375rem;
  }
}

@media (min-width: 420px) {
  .card-icon i {
    font-size: 1.8rem;
  }
}
</style>
