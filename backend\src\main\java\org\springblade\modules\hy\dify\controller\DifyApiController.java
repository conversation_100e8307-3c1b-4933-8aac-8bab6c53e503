package org.springblade.modules.hy.dify.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springblade.modules.req.DifyFile;
import org.springblade.modules.resp.BlockResponse;
import org.springblade.modules.resp.StreamResponse;
import org.springblade.modules.security.jwt.JwtTokenProvider;
import org.springblade.modules.service.DifyService;
import org.springblade.modules.service.RedisService;
import org.springblade.qyweixin.security.CustomUserDetails;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/enclosure/dify")
@RequiredArgsConstructor
public class DifyApiController {

    private final HttpServletRequest request;

    private final HttpServletResponse response;

    private final DifyService difyService;


    private final RedisService redisService;

    /**
     * 阻塞式调用
     *
     * @param query
     * @param key
     * @param conversationId
     * @return
     */
    @GetMapping("/block")
    public String blockApi(@RequestParam String query, @RequestParam String key, @RequestParam String conversationId) {

        UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
        CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
        BlockResponse blockResponse = difyService.blockingMessage(query, principal.getAttributes().getName(), key, conversationId);
        return blockResponse.getAnswer();
    }



}
