package org.springblade.modules.dingding.service;

import org.springblade.core.mp.base.BaseService;
import org.springblade.core.tool.support.Kv;
import org.springblade.modules.dingding.entity.DingAppUser;
import org.springblade.modules.dingding.vo.DingUserAccessTokenVO;
import org.springblade.modules.dingding.vo.DingUserInfoVO;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-01-22 10:18
 */
public interface IDingAppUserService extends BaseService<DingAppUser> {

    DingUserAccessTokenVO getAccessToken() throws Exception;

    DingUserInfoVO getUserInfo(String authCode, String accessToken) throws Exception;

    Kv login(String authCode);

    String getJsapiTicket() throws Exception;

}
