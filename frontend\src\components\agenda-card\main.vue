<template>  
  <div class="timeline-container">
    <h2 class="timeline-title">当前议程</h2>
    <div class="timeline-content">
      <div class="timeline-item current">
        <div class="time-column">
          <div class="start-time">10:30</div>
          <div class="end-time">12:00</div>
        </div>
        <div class="divider"></div>
        <div class="content-column">
          <h3>签到注册</h3>
          <p>参会者签到，领取会议资料</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 时间线容器 */
.timeline-container {
  width: 100%;
  padding: 2vw;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.12);
  
  /* 保持18%屏幕高度 */
  height: 18vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* 标题样式 */
.timeline-title {
  text-align: center;
  color: #ffffff;
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  padding-bottom: 2vw;
  border-bottom: 1px solid rgba(218, 233, 255, 0.4);
  font-size: 1.2rem;
  letter-spacing: 0.5px;
}

/* 主内容区域 */
.timeline-content {
  flex: 1;
  display: flex;
  align-items: center;
  width: 100%;
}

/* 时间线项样式 */
.timeline-item {
  display: flex;
  align-items: center;
  width: 100%;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards;
  padding: 0 2vw;
}

.timeline-item.current {
  animation-delay: 0.2s;
}

/* 左侧时间列 */
.time-column {
  flex: 0 0 auto;
  text-align: right;
  padding-right: 2vw;
  margin-right: 0;
}

/* 时间样式 */
.start-time, .end-time {
  color: #ffffff;
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0;
  line-height: 1.2;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 分割条样式 */
.divider {
  width: 2px;
  height: 15vw;
  max-height: 60px;
  background: linear-gradient(to bottom, #004ffc, #4d7fff);
  margin: 0 1.5vw; /* 减少分割线两侧间距 */
  border-radius: 1px;
  box-shadow: 0 0 8px rgba(0, 79, 252, 0.5);
}

/* 右侧内容列 */
.content-column {
  flex: 1;
  padding: 2vw 3vw;
  background: rgba(255, 255, 255, 0.07);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: left;
}

/* 议程名称 */
.content-column h3 {
  color: #ffffff;
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  margin: 0 0 1vw;
  font-size: 1rem;
  letter-spacing: 0.3px;
}

/* 议程详情 */
.content-column p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin: 0;
  font-size: 0.75rem;
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media (max-width: 320px) {
  .timeline-container {
    height: 25vh;
    min-height: 140px;
  }
  
  .time-column {
    padding-right: 1.5vw;
  }
  
  .start-time, .end-time {
    font-size: 0.85rem;
  }
  
  .divider {
    margin: 0 1vw;
    height: 12vw;
  }
  
  .content-column {
    padding: 1.5vw 2vw;
  }
  
  .content-column h3 {
    font-size: 0.85rem;
  }
  
  .content-column p {
    font-size: 0.7rem;
  }
}

@media (min-width: 480px) {
  .start-time, .end-time {
    font-size: 1rem;
  }
  
  .content-column h3 {
    font-size: 1rem;
  }
  
  .content-column p {
    font-size: 0.8rem;
  }
}
</style>
