<template>
  <div class="blog-slider swiper">
    <div class="blog-slider__wrp swiper-wrapper">
      <div class="blog-slider__item swiper-slide" v-for="(slide, index) in slides" :key="index">
        <div class="blog-slider__card">
          <div class="blog-slider__img">
            <img :src="slide.image" alt="slide image" />
          </div>
          <div class="blog-slider__content">
            <span class="blog-slider__code">{{ slide.date }}</span>
            <div class="blog-slider__title">{{ slide.title }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="blog-slider__pagination swiper-pagination"></div> -->
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import 'swiper/swiper-bundle.css';
import Swiper from "swiper/bundle";

const slides = ref([
  {
    image: "img/banner.png",
    date: "2025年9月",
    title: "主办单位：广东烟草广州市有限公司",
  },
  {
    image: "img/banner.png",
    date: "26 December 2019",
    title: "Lorem Ipsum Dolor2",
  },
  {
    image: "img/banner.png",
    date: "26 December 2019",
    title: "Lorem Ipsum Dolor3",
  },
]);

onMounted(() => {
  new Swiper(".blog-slider.swiper", {
    spaceBetween: 30,
    effect: "slide",
    loop: true,
    mousewheel: {
      invert: false,
    },
    pagination: {
      el: ".blog-slider__pagination.swiper-pagination",
      clickable: true,
    },
  });
});
</script>

<style scoped>
.blog-slider {
  width: 100%;
  max-width: 800px;
  margin: 2rem auto;
  position: relative;
  overflow: visible;
}

.blog-slider__item {
  display: flex;
  justify-content: center;
  overflow: visible;
}

.blog-slider__card {
  position: relative;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(12px);
  border-radius: 25px;
  box-shadow: 0 0 10px rgba(123, 0, 255, 0.5);
  width: 100%;
  max-width: 600px;
  text-align: center;
  padding-top: 5rem;
  padding-bottom: 1.5rem;
  overflow: visible;
}

.blog-slider__img {
  position: absolute;
  top: -60px;
  left: 50%;
  transform: translateX(-50%);
  width: 70%;
  z-index: 2;
}

.blog-slider__img img {
  width: 100%;
  height: auto;
  border-radius: 15px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  background: white;
  object-fit: contain;
}

.blog-slider__content {
  padding-top: 2rem;
  color: #fff;
}

.blog-slider__code {
  color: #ddd;
  font-weight: 500;
  margin-bottom: 10px;
  display: block;
  font-size: 14px;
}

.blog-slider__title {
  font-size: 18px;
  font-weight: 700;
  color: #fff;
}

.blog-slider__pagination {
  position: relative;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  gap: 6px;
}

.blog-slider__pagination .swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  background: #ccc;
  opacity: 0.5;
  border-radius: 50%;
  transition: all 0.3s;
}

.blog-slider__pagination .swiper-pagination-bullet-active {
  background: #7b00ff;
  opacity: 1;
  width: 25px;
  border-radius: 20px;
}
</style>
