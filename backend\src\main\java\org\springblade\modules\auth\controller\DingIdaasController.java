package org.springblade.modules.auth.controller;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.request.AuthRequest;
import me.zhyd.oauth.utils.AuthStateUtils;
import org.springblade.core.social.utils.SocialUtil;
import org.springblade.modules.auth.request.IdaasRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
@Slf4j
@RestController
@RequestMapping("/dingIdaas")
public class DingIdaasController {


    @Value("${idaas.pic.client}")
    private String picClient;
    @Value("${idaas.pic.secret}")
    private String picSecret;
    @Value("${idaas.pic.redirect}")
    private String picRedirect;
    @Value("${idaas.pic.server}")
    private String picIDaaSServer;
    @Value("${idaas.pic.login}")
    private String picLogin;

    @GetMapping("/render")
    public void render(HttpServletResponse response, HttpServletRequest request) throws IOException {
        log.info("钉钉 H5 登录重定向到 统一登录页入口");
        String redirect = "";

        AuthConfig authConfig = new AuthConfig();
        authConfig.setClientId(this.picClient);
        authConfig.setClientSecret(this.picSecret);

        redirect = this.picRedirect;

        authConfig.setRedirectUri(redirect);
        authConfig.setIgnoreCheckState(true);

        AuthRequest authRequest = new IdaasRequest(authConfig, SocialUtil.getAuthStateCache(), this.picIDaaSServer);
        String authorizeUrl = authRequest.authorize(AuthStateUtils.createState());
        log.info("钉钉 H5 登录重定向到 统一登录页入口 {}", authorizeUrl);
        response.sendRedirect(authorizeUrl);
    }

}
