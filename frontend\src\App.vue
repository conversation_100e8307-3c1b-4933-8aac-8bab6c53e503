<template>
  <!-- Stagewise AI辅助编辑工具栏 (仅在开发环境显示) -->
  <!-- <StagewiseToolbar /> -->

  <router-view />
</template>

<script setup>
import { StagewiseToolbar } from "@stagewise/toolbar-vue";

// Stagewise配置 - 仅在开发环境启用
// if (import.meta.env.DEV) {
//   console.log("Stagewise: 开发环境检测到，正在初始化...");
// }

onMounted(() => {
  const setBaseFontSize = () => {
    const baseWidth = 430; // 基准宽度(iphone 14 pro max)
    const clientWidth = document.documentElement.clientWidth;
    const fontSize = (clientWidth / baseWidth) * 16; // 基准字体大小为16px
    document.documentElement.style.fontSize = `${fontSize}px`;
  };

  window.addEventListener("resize", setBaseFontSize);
});
</script>

<style>
html,
body,
#app {
  width: 100%;
  height: 100%;
  overflow: hidden; /* 禁止全局滚动 */
}
</style>
