/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (bladeja<PERSON>@qq.com)
 */
package org.springblade.modules.hy.dinner.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.annotation.PreAuth;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.hy.dinner.pojo.entity.DinnerEntity;
import org.springblade.modules.hy.dinner.pojo.vo.DinnerVO;
import org.springblade.modules.hy.dinner.excel.DinnerExcel;
import org.springblade.modules.hy.dinner.wrapper.DinnerWrapper;
import org.springblade.modules.hy.dinner.service.IDinnerService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.excel.util.ExcelUtil;
import org.springblade.core.tool.constant.BladeConstant;
import org.springblade.core.tool.constant.RoleConstant;
import java.util.Map;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 用餐管理表 控制器
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@RestController
@AllArgsConstructor
@RequestMapping("hy/dinner")
@Tag(name = "用餐管理表", description = "用餐管理表接口")
public class DinnerController extends BladeController {

	private final IDinnerService dinnerService;

	/**
	 * 用餐管理表 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description  = "传入dinner")
	public R<DinnerVO> detail(DinnerEntity dinner) {
		DinnerEntity detail = dinnerService.getOne(Condition.getQueryWrapper(dinner));
		return R.data(DinnerWrapper.build().entityVO(detail));
	}
	/**
	 * 用餐管理表 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description  = "传入dinner")
	public R<IPage<DinnerVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> dinner, Query query) {
		// 处理查询参数的类型转换
		DinnerWrapper.build().dinnerQuery(dinner);
		IPage<DinnerEntity> pages = dinnerService.page(Condition.getPage(query), Condition.getQueryWrapper(dinner, DinnerEntity.class));
		return R.data(DinnerWrapper.build().pageVO(pages));
	}

	/**
	 * 用餐管理表 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description  = "传入dinner")
	public R<IPage<DinnerVO>> page(DinnerVO dinner, Query query) {
		IPage<DinnerVO> pages = dinnerService.selectDinnerPage(Condition.getPage(query), dinner);
		return R.data(pages);
	}

	/**
	 * 用餐管理表 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description  = "传入dinner")
	public R save(@Valid @RequestBody DinnerEntity dinner) {
		return R.status(dinnerService.save(dinner));
	}

	/**
	 * 用餐管理表 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description  = "传入dinner")
	public R update(@Valid @RequestBody DinnerEntity dinner) {
		return R.status(dinnerService.updateById(dinner));
	}

	/**
	 * 用餐管理表 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description  = "传入dinner")
	public R submit(@Valid @RequestBody DinnerEntity dinner) {
		return R.status(dinnerService.saveOrUpdate(dinner));
	}

	/**
	 * 用餐管理表 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description  = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(dinnerService.deleteLogic(Func.toLongList(ids)));
	}


	/**
	 * 导出数据
	 */
	@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
	@GetMapping("/export-dinner")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "导出数据", description  = "传入dinner")
	public void exportDinner(@Parameter(hidden = true) @RequestParam Map<String, Object> dinner, BladeUser bladeUser, HttpServletResponse response) {
		QueryWrapper<DinnerEntity> queryWrapper = Condition.getQueryWrapper(dinner, DinnerEntity.class);
		//if (!AuthUtil.isAdministrator()) {
		//	queryWrapper.lambda().eq(Dinner::getTenantId, bladeUser.getTenantId());
		//}
		//queryWrapper.lambda().eq(DinnerEntity::getIsDeleted, BladeConstant.DB_NOT_DELETED);
		List<DinnerExcel> list = dinnerService.exportDinner(queryWrapper);
		ExcelUtil.export(response, "用餐管理表数据" + DateUtil.time(), "用餐管理表数据表", list, DinnerExcel.class);
	}

}
