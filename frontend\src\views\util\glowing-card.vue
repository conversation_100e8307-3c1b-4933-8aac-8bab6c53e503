<template>
  <div
    class="glowing-card"
    :style="{ '--glow': color || '#7b00ff' }"
    @click="$emit('navigate')"
  >
    <div class="card-content">
      <div class="card-icon">
        <i :class="iconClass"></i>
      </div>
      <div class="card-title">{{ title }}</div>
      <div class="card-subtitle">{{ subtitle }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GlowingCard',
  props: {
    iconClass: String,
    title: String,
    subtitle: String,
    color: String
  }
}
</script>

<style scoped>
.glowing-card {
  width: 100%;
  min-height: 7rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid var(--glow);
  box-shadow: 0 0 10px var(--glow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.glowing-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 0 20px var(--glow);
}

.card-content {
  text-align: center;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card-icon i {
  font-size: 28px;
  color: var(--glow);
  margin-bottom: 10px;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
}

.card-subtitle {
  font-size: 11px;
  opacity: 0.8;
}
</style>
