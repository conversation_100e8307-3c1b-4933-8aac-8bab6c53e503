#数据源配置
spring:
  data:
    redis:
      ##redis 单机环境配置
      host: 127.0.0.1
      port: 6379
      password:
      database: 10
      ssl:
        enabled: false
      ##redis 集群环境配置
      #cluster:
      #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
      #  commandTimeout: 5000
  datasource:
    # MySql
#    url: *********************************************************************************************************************************************************************************************************************************************
#    username: root
#    password: root
    # PostgreSQL
    url: ***************************************************************
    username: gzyc
    password: gzyc1234
    # Oracle
    #url: *************************************
    #username: BLADEX_BOOT
    #password: BLADEX_BOOT
    # SqlServer
    #url: ********************************************************
    #username: bladex_boot
    #password: bladex_boot
    # DaMeng
    #url: jdbc:dm://127.0.0.1:5236/BLADEX_BOOT?zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8
    #username: BLADEX_BOOT
    #password: BLADEX_BOOT
    # YashanDB
    #url: ***************************************
    #username: BLADEX_BOOT
    #password: BLADEX_BOOT

#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:1888

#blade配置
blade:
  #分布式锁配置
  lock:
    ##是否启用分布式锁
    enabled: false
    ##redis服务地址
    address: redis://127.0.0.1:6379
  #本地文件上传
  file:
    remote-mode: true
    upload-domain: http://localhost:8999
#    remote-path: /usr/share/nginx/html
    remote-path: D:/uploads/

#oss默认配置
oss:
  #开启oss配置
  enabled: true
  #开启oss类型
  #minio、s3、qiniu、alioss、huaweiobs、tencentcos
  name: minio
  #租户模式
  tenant-mode: false
  #oss服务地址
  endpoint: http://127.0.0.1:9000
  #minio转换服务地址，用于内网上传后将返回地址改为转换的外网地址
  transform-endpoint: https://tcinspect.foshantc.com/webfile
  #访问key
  access-key: XkVpnq7Sksz2FhCwDylS
  #密钥key
  secret-key: ibjpQkU6t3mcD02WqCSncgpQwhZARQozfeFCLTvq
  #存储桶
  bucket-name: daofeng

ding-app:
  agent-id: ${DING_APP_AGENT_ID:3959288029}
  mini-app-id: ${DING_APP_MINI_APP_ID:5000000006276235}
  app-key: ${DING_APP_APP_KEY:dingcnqysdhzfhrgl08h}
  app-secret: ${DING_APP_APP_SECRET:o0pEVXFlQll3gqRDv4GfJopAtZ3RFzfmSOGVLVNQwkVU9zvkgE_6H2ywgb8YiF_B}
  corp-id: ${DING_APP_CORP_ID:ding8a6f1b536f891ffeee0f45d8e4f7c288}
  api-token: ${DING_APP_API_TOKEN:a27dac1fad2033a3a762e165e308bdea}
  #二维码配置
  qrcode:
    #移动端签到页面基础URL
    mobile-base-url: ${QR_MOBILE_BASE_URL:http://localhost:2888}