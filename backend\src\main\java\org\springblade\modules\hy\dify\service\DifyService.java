package org.springblade.modules.hy.dify.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.entity.Account;
import org.springblade.modules.entity.AgentDict;
import org.springblade.modules.req.DifyFile;
import org.springblade.modules.req.DifyRequestBody;
import org.springblade.modules.resp.BlockResponse;
import org.springblade.modules.resp.StreamResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
@RequiredArgsConstructor
@Slf4j
public class DifyService {

    private final AgentDictService agentDictService;

    private final RestTemplate restTemplate;

    private final WebClient webClient;

    @Autowired
    private RedisService redisService;

//    private final HashMap<String, String> fileContentMaps = new HashMap<>();



    /**
     * 阻塞式调用dify.
     *
     * @param query  查询文本
     * @return BlockResponse
     */
    public BlockResponse blockingMessage(String query, String userName, String agentId, String conversationId) {
        if (StrUtil.isBlank(agentId)) {
            return null;
        }

        AgentDict agentDict = agentDictService.findById(agentId);
        if (agentDict == null) {
            return null;
        }

        //1.设置请求体
        DifyRequestBody body = new DifyRequestBody();
        body.setInputs(new HashMap<>());
        body.setQuery(query);
        body.setResponseMode("blocking");
        body.setConversationId(conversationId);
        body.setUser(userName);
        //2.设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(List.of(MediaType.APPLICATION_JSON));
        headers.setBearerAuth(agentDict.getApiKey());
        //3.封装请求体和请求头
        String jsonString = JSON.toJSONString(body);
        HttpEntity<String> entity = new HttpEntity<>(jsonString, headers);
        //4.发送post请求，阻塞式
        ResponseEntity<BlockResponse> stringResponseEntity =
                restTemplate.postForEntity(agentDict.getApiServerHost() + "/chat-messages", entity, BlockResponse.class);
        //5.返回响应体
        return stringResponseEntity.getBody();
    }


    /**
     * 停止响应
     * @param agentId
     * @param taskId
     * @param user
     * @return
     */
    public Integer stop(String agentId, String taskId, String user) {
        if (StrUtil.isBlank(agentId)) {
            return null;
        }

        AgentDict agentDict = agentDictService.findById(agentId);
        if (agentDict == null) {
            return null;
        }

        try {
            // 1. 设置请求体
            String jsonInputString = "{\"user\": \"" + user + "\"}";

            // 2. 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(agentDict.getApiKey());

            // 3. 封装请求体和请求头
            HttpEntity<String> entity = new HttpEntity<>(jsonInputString, headers);


            // 4. 发送 POST 请求
            String url = agentDict.getApiServerHost() + "/chat-messages/" + taskId + "/stop";
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            // 5. 返回响应码
            Integer responseCode = response.getStatusCode().value();
            System.out.println("响应码: " + responseCode);
            return responseCode;
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return 401;
    }




}
