export default {
  height: 'auto',
  calcHeight: 30,
  tip: false,
  searchShow: false,
  border: true,
  index: true,
  viewBtn: true,
  selection: false,
  dialogClickModal: false,
  column: [
    {
      label: "主键",
      prop: "id",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "用餐日期",
      prop: "dinnerDate",
      type: "date",
      format: "YYYY-MM-DD",
      valueFormat: "YYYY-MM-DD HH:mm:ss",
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      rules: [
        {
          required: true,
          message: '请选择用餐日期',
          trigger: 'change',
        },
      ],
    },
    {
      label: "用户ID",
      prop: "userId",
      type: "input",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      hide: true,
    },
    {
      label: "早餐状态",
      prop: "breakfastStatus",
      type: "select",
      dicData: [
        { label: '未用餐', value: 0 },
        { label: '已用餐', value: 1 }
      ],
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      formatter: (_, __, cellValue) => {
        return cellValue === 1 ? '已用餐' : '未用餐';
      },
    },
    {
      label: "早餐二维码",
      prop: "breakfastQrCode",
      type: "input",
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
    },
    {
      label: "中餐状态",
      prop: "lunchStatus",
      type: "select",
      dicData: [
        { label: '未用餐', value: 0 },
        { label: '已用餐', value: 1 }
      ],
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      formatter: (_, __, cellValue) => {
        return cellValue === 1 ? '已用餐' : '未用餐';
      },
    },
    {
      label: "中餐二维码",
      prop: "lunchQrCode",
      type: "input",
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
    },
    {
      label: "晚餐状态",
      prop: "dinnerStatus",
      type: "select",
      dicData: [
        { label: '未用餐', value: 0 },
        { label: '已用餐', value: 1 }
      ],
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      formatter: (_, __, cellValue) => {
        return cellValue === 1 ? '已用餐' : '未用餐';
      },
    },
    {
      label: "晚餐二维码",
      prop: "dinnerQrCode",
      type: "input",
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
    },
    {
      label: "创建时间",
      prop: "createTime",
      type: "datetime",
      format: "YYYY-MM-DD HH:mm:ss",
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,
    },
  ]
}
