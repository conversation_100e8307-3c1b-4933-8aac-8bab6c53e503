-- H5端8大模块表结构（PostgreSQL，含详细注释）
-- 每个表均包含通用字段：create_user, create_dept, create_time, update_user, update_time, status, is_deleted

-- 1. 会议议程
DROP TABLE IF EXISTS "hy_agenda";
CREATE TABLE hy_agenda (
    id SERIAL PRIMARY KEY,                -- 主键，自增
    date DATE NOT NULL,                   -- 会议日期
    start_time TIME NOT NULL,             -- 开始时间
    end_time TIME NOT NULL,               -- 结束时间
    topic VARCHAR(200) NOT NULL,          -- 议题/主题
    speaker VARCHAR(100),                 -- 演讲人
    venue VARCHAR(100),                   -- 会场
    description TEXT,                     -- 议程描述
    create_user BIGINT,                   -- 创建人
    create_dept BIGINT,                   -- 创建部门
    create_time TIMESTAMP(6),             -- 创建时间
    update_user BIGINT,                   -- 更新人
    update_time TIMESTAMP(6),             -- 更新时间
    status INT DEFAULT 1,                 -- 状态
    is_deleted INT DEFAULT 0              -- 是否删除
);
COMMENT ON TABLE hy_agenda IS '会议议程表';
COMMENT ON COLUMN hy_agenda.id IS '主键，自增';
COMMENT ON COLUMN hy_agenda.date IS '会议日期';
COMMENT ON COLUMN hy_agenda.start_time IS '开始时间';
COMMENT ON COLUMN hy_agenda.end_time IS '结束时间';
COMMENT ON COLUMN hy_agenda.topic IS '议题/主题';
COMMENT ON COLUMN hy_agenda.speaker IS '演讲人';
COMMENT ON COLUMN hy_agenda.venue IS '会场';
COMMENT ON COLUMN hy_agenda.description IS '议程描述';
COMMENT ON COLUMN hy_agenda.create_user IS '创建人';
COMMENT ON COLUMN hy_agenda.create_dept IS '创建部门';
COMMENT ON COLUMN hy_agenda.create_time IS '创建时间';
COMMENT ON COLUMN hy_agenda.update_user IS '更新人';
COMMENT ON COLUMN hy_agenda.update_time IS '更新时间';
COMMENT ON COLUMN hy_agenda.status IS '状态';
COMMENT ON COLUMN hy_agenda.is_deleted IS '是否删除';

-- 2. 云直播
DROP TABLE IF EXISTS "hy_live_stream";
CREATE TABLE hy_live_stream (
    id SERIAL PRIMARY KEY,                -- 主键，自增
    title VARCHAR(200) NOT NULL,          -- 直播标题
    url VARCHAR(300) NOT NULL,            -- 直播地址
    start_time TIMESTAMP NOT NULL,        -- 开始时间
    end_time TIMESTAMP,                   -- 结束时间
    status_text VARCHAR(20) DEFAULT '未开始',  -- 直播状态
    description TEXT,                     -- 直播描述
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);
COMMENT ON TABLE hy_live_stream IS '云直播信息表';
COMMENT ON COLUMN hy_live_stream.id IS '主键，自增';
COMMENT ON COLUMN hy_live_stream.title IS '直播标题';
COMMENT ON COLUMN hy_live_stream.url IS '直播地址';
COMMENT ON COLUMN hy_live_stream.start_time IS '开始时间';
COMMENT ON COLUMN hy_live_stream.end_time IS '结束时间';
COMMENT ON COLUMN hy_live_stream.status_text IS '直播状态';
COMMENT ON COLUMN hy_live_stream.description IS '直播描述';
COMMENT ON COLUMN hy_live_stream.create_user IS '创建人';
COMMENT ON COLUMN hy_live_stream.create_dept IS '创建部门';
COMMENT ON COLUMN hy_live_stream.create_time IS '创建时间';
COMMENT ON COLUMN hy_live_stream.update_user IS '更新人';
COMMENT ON COLUMN hy_live_stream.update_time IS '更新时间';
COMMENT ON COLUMN hy_live_stream.status IS '状态';
COMMENT ON COLUMN hy_live_stream.is_deleted IS '是否删除';

-- 3. 分会场信息
DROP TABLE IF EXISTS "hy_sub_venue";
CREATE TABLE hy_sub_venue (
    id SERIAL PRIMARY KEY,                -- 主键，自增
    name VARCHAR(100) NOT NULL,           -- 分会场名称
    location VARCHAR(200),                -- 地点
    hy_agenda_id INTEGER REFERENCES hy_agenda(id), -- 关联议程
    manager VARCHAR(100),                 -- 负责人
    description TEXT,                     -- 描述
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);
COMMENT ON TABLE hy_sub_venue IS '分会场信息表';
COMMENT ON COLUMN hy_sub_venue.id IS '主键，自增';
COMMENT ON COLUMN hy_sub_venue.name IS '分会场名称';
COMMENT ON COLUMN hy_sub_venue.location IS '地点';
COMMENT ON COLUMN hy_sub_venue.hy_agenda_id IS '关联议程';
COMMENT ON COLUMN hy_sub_venue.manager IS '负责人';
COMMENT ON COLUMN hy_sub_venue.description IS '描述';
COMMENT ON COLUMN hy_sub_venue.create_user IS '创建人';
COMMENT ON COLUMN hy_sub_venue.create_dept IS '创建部门';
COMMENT ON COLUMN hy_sub_venue.create_time IS '创建时间';
COMMENT ON COLUMN hy_sub_venue.update_user IS '更新人';
COMMENT ON COLUMN hy_sub_venue.update_time IS '更新时间';
COMMENT ON COLUMN hy_sub_venue.status IS '状态';
COMMENT ON COLUMN hy_sub_venue.is_deleted IS '是否删除';

-- 4. 会议资料
DROP TABLE IF EXISTS "hy_materials";
CREATE TABLE hy_materials (
    id SERIAL PRIMARY KEY,                -- 主键，自增
    title VARCHAR(200) NOT NULL,          -- 资料名称
    file_url VARCHAR(300) NOT NULL,       -- 文件地址
    upload_time TIMESTAMP DEFAULT NOW(),  -- 上传时间
    uploader VARCHAR(100),                -- 上传人
    description TEXT,                     -- 描述
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);
COMMENT ON TABLE hy_materials IS '会议资料表';
COMMENT ON COLUMN hy_materials.id IS '主键，自增';
COMMENT ON COLUMN hy_materials.title IS '资料名称';
COMMENT ON COLUMN hy_materials.file_url IS '文件地址';
COMMENT ON COLUMN hy_materials.upload_time IS '上传时间';
COMMENT ON COLUMN hy_materials.uploader IS '上传人';
COMMENT ON COLUMN hy_materials.description IS '描述';
COMMENT ON COLUMN hy_materials.create_user IS '创建人';
COMMENT ON COLUMN hy_materials.create_dept IS '创建部门';
COMMENT ON COLUMN hy_materials.create_time IS '创建时间';
COMMENT ON COLUMN hy_materials.update_user IS '更新人';
COMMENT ON COLUMN hy_materials.update_time IS '更新时间';
COMMENT ON COLUMN hy_materials.status IS '状态';
COMMENT ON COLUMN hy_materials.is_deleted IS '是否删除';

-- 5. 在线相册
DROP TABLE IF EXISTS "hy_photo_album";
CREATE TABLE hy_photo_album (
    id SERIAL PRIMARY KEY,                -- 主键，自增
    title VARCHAR(200) NOT NULL,          -- 相册名称
    image_url VARCHAR(300) NOT NULL,      -- 图片地址
    upload_time TIMESTAMP DEFAULT NOW(),  -- 上传时间
    uploader VARCHAR(100),                -- 上传人
    category VARCHAR(50),                 -- 分类
    description TEXT,                     -- 描述
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);
COMMENT ON TABLE hy_photo_album IS '会议相册表';
COMMENT ON COLUMN hy_photo_album.id IS '主键，自增';
COMMENT ON COLUMN hy_photo_album.title IS '相册名称';
COMMENT ON COLUMN hy_photo_album.image_url IS '图片地址';
COMMENT ON COLUMN hy_photo_album.upload_time IS '上传时间';
COMMENT ON COLUMN hy_photo_album.uploader IS '上传人';
COMMENT ON COLUMN hy_photo_album.category IS '分类';
COMMENT ON COLUMN hy_photo_album.description IS '描述';
COMMENT ON COLUMN hy_photo_album.create_user IS '创建人';
COMMENT ON COLUMN hy_photo_album.create_dept IS '创建部门';
COMMENT ON COLUMN hy_photo_album.create_time IS '创建时间';
COMMENT ON COLUMN hy_photo_album.update_user IS '更新人';
COMMENT ON COLUMN hy_photo_album.update_time IS '更新时间';
COMMENT ON COLUMN hy_photo_album.status IS '状态';
COMMENT ON COLUMN hy_photo_album.is_deleted IS '是否删除';

-- 6. 参会指南
DROP TABLE IF EXISTS "hy_guide";
CREATE TABLE hy_guide (
    id SERIAL PRIMARY KEY,                -- 主键，自增
    title VARCHAR(200) NOT NULL,          -- 指南标题
    content TEXT NOT NULL,                -- 内容
    type VARCHAR(50),                     -- 类型
    update_time TIMESTAMP DEFAULT NOW(),  -- 更新时间
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6),
    update_user BIGINT,
    -- update_time 字段已在上方定义
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);
COMMENT ON TABLE hy_guide IS '参会指南表';
COMMENT ON COLUMN hy_guide.id IS '主键，自增';
COMMENT ON COLUMN hy_guide.title IS '指南标题';
COMMENT ON COLUMN hy_guide.content IS '内容';
COMMENT ON COLUMN hy_guide.type IS '类型';
COMMENT ON COLUMN hy_guide.update_time IS '更新时间';
COMMENT ON COLUMN hy_guide.create_user IS '创建人';
COMMENT ON COLUMN hy_guide.create_dept IS '创建部门';
COMMENT ON COLUMN hy_guide.create_time IS '创建时间';
COMMENT ON COLUMN hy_guide.update_user IS '更新人';
COMMENT ON COLUMN hy_guide.status IS '状态';
COMMENT ON COLUMN hy_guide.is_deleted IS '是否删除';

-- 7. 会务助手
DROP TABLE IF EXISTS "hy_ai_chat_log";
CREATE TABLE hy_ai_chat_log (
    id SERIAL PRIMARY KEY,                -- 主键，自增
    user_id INTEGER,                      -- 用户ID
    question TEXT NOT NULL,               -- 提问内容
    answer TEXT,                          -- AI回复
    create_time TIMESTAMP DEFAULT NOW(),  -- 提问时间
    create_user BIGINT,
    create_dept BIGINT,
    -- create_time 字段已在上方定义
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);
COMMENT ON TABLE hy_ai_chat_log IS 'AI会务助手问答日志表';
COMMENT ON COLUMN hy_ai_chat_log.id IS '主键，自增';
COMMENT ON COLUMN hy_ai_chat_log.user_id IS '用户ID';
COMMENT ON COLUMN hy_ai_chat_log.question IS '提问内容';
COMMENT ON COLUMN hy_ai_chat_log.answer IS 'AI回复';
COMMENT ON COLUMN hy_ai_chat_log.create_time IS '提问时间';
COMMENT ON COLUMN hy_ai_chat_log.create_user IS '创建人';
COMMENT ON COLUMN hy_ai_chat_log.create_dept IS '创建部门';
COMMENT ON COLUMN hy_ai_chat_log.update_user IS '更新人';
COMMENT ON COLUMN hy_ai_chat_log.update_time IS '更新时间';
COMMENT ON COLUMN hy_ai_chat_log.status IS '状态';
COMMENT ON COLUMN hy_ai_chat_log.is_deleted IS '是否删除';

-- 8. 个人中心
DROP TABLE IF EXISTS "hy_user_profile";
CREATE TABLE hy_user_profile (
    id SERIAL PRIMARY KEY,                -- 主键，自增
    name VARCHAR(100) NOT NULL,           -- 姓名
    phone VARCHAR(20),                    -- 手机号
    email VARCHAR(100),                   -- 邮箱
    avatar VARCHAR(300),                  -- 头像
    company VARCHAR(100),                 -- 单位
    position VARCHAR(100),                -- 职位
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);
COMMENT ON TABLE hy_user_profile IS '用户个人信息表';
COMMENT ON COLUMN hy_user_profile.id IS '主键，自增';
COMMENT ON COLUMN hy_user_profile.name IS '姓名';
COMMENT ON COLUMN hy_user_profile.phone IS '手机号';
COMMENT ON COLUMN hy_user_profile.email IS '邮箱';
COMMENT ON COLUMN hy_user_profile.avatar IS '头像';
COMMENT ON COLUMN hy_user_profile.company IS '单位';
COMMENT ON COLUMN hy_user_profile.position IS '职位';
COMMENT ON COLUMN hy_user_profile.create_user IS '创建人';
COMMENT ON COLUMN hy_user_profile.create_dept IS '创建部门';
COMMENT ON COLUMN hy_user_profile.create_time IS '创建时间';
COMMENT ON COLUMN hy_user_profile.update_user IS '更新人';
COMMENT ON COLUMN hy_user_profile.update_time IS '更新时间';
COMMENT ON COLUMN hy_user_profile.status IS '状态';
COMMENT ON COLUMN hy_user_profile.is_deleted IS '是否删除';

DROP TABLE IF EXISTS "hy_attendance_record";
CREATE TABLE hy_attendance_record (
    id SERIAL PRIMARY KEY,                -- 主键，自增
    user_id INTEGER REFERENCES hy_user_profile(id), -- 用户ID
    hy_agenda_id INTEGER REFERENCES hy_agenda(id),     -- 议程ID
    checkin_time TIMESTAMP,               -- 签到时间
    status_text VARCHAR(20),              -- 签到状态
    create_user BIGINT,
    create_dept BIGINT,
    create_time TIMESTAMP(6),
    update_user BIGINT,
    update_time TIMESTAMP(6),
    status INT DEFAULT 1,
    is_deleted INT DEFAULT 0
);
COMMENT ON TABLE hy_attendance_record IS '参会签到记录表';
COMMENT ON COLUMN hy_attendance_record.id IS '主键，自增';
COMMENT ON COLUMN hy_attendance_record.user_id IS '用户ID';
COMMENT ON COLUMN hy_attendance_record.hy_agenda_id IS '议程ID';
COMMENT ON COLUMN hy_attendance_record.checkin_time IS '签到时间';
COMMENT ON COLUMN hy_attendance_record.status_text IS '签到状态';
COMMENT ON COLUMN hy_attendance_record.create_user IS '创建人';
COMMENT ON COLUMN hy_attendance_record.create_dept IS '创建部门';
COMMENT ON COLUMN hy_attendance_record.create_time IS '创建时间';
COMMENT ON COLUMN hy_attendance_record.update_user IS '更新人';
COMMENT ON COLUMN hy_attendance_record.update_time IS '更新时间';
COMMENT ON COLUMN hy_attendance_record.status IS '状态';
COMMENT ON COLUMN hy_attendance_record.is_deleted IS '是否删除';