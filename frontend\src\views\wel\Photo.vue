<template>
  <div class="page-container">
    <main class="page-content">
      <div class="list-container">
        <div class="form-header">
          <div class="icon-with-text">
            <i class="fas fa-images"></i>
            <h2>{{ mainTitle || "会议相册" }}</h2>
          </div>
        </div>

        <!-- 相册分类 -->
        <div class="album-categories">
          <button
            class="category-btn"
            :class="{ active: activeCategory === 'all' }"
            @click="switchCategory('all')"
          >
            全部
          </button>
          <button
            class="category-btn"
            :class="{ active: activeCategory === 'opening' }"
            @click="switchCategory('opening')"
          >
            开幕式
          </button>
          <button
            class="category-btn"
            :class="{ active: activeCategory === 'speech' }"
            @click="switchCategory('speech')"
          >
            主题演讲
          </button>
          <button
            class="category-btn"
            :class="{ active: activeCategory === 'discussion' }"
            @click="switchCategory('discussion')"
          >
            讨论交流
          </button>
          <button
            class="category-btn"
            :class="{ active: activeCategory === 'group' }"
            @click="switchCategory('group')"
          >
            合影留念
          </button>
        </div>



        <!-- 照片网格滚动容器 -->
        <div class="photos-scroll-container">
          <div class="photo-grid" id="photoGrid">
            <div
              v-for="photo in filteredPhotos"
              :key="photo.id"
              class="photo-item"
              @click="openLightbox(photo)"
            >
              <img :src="photo.url" :alt="photo.title" class="photo-img" />
              <div class="photo-overlay">
                <div class="photo-info">
                  <h3>{{ photo.title }}</h3>
                  <p>{{ photo.date }}</p>
                </div>
                <div class="photo-actions">
                  <button class="action-btn" @click.stop="likePhoto(photo)">
                    <i class="fas fa-heart" :class="{ liked: photo.liked }"></i>
                    {{ photo.likes }}
                  </button>
                  <button class="action-btn" @click.stop="downloadPhoto(photo)">
                    <i class="fas fa-download"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="photo-stats">
          <div class="stat-item">
            <i class="fas fa-images"></i>
            <span
              >总照片数：<strong>{{ totalPhotos }}</strong></span
            >
          </div>
          <!-- <div class="stat-item">
            <i class="fas fa-heart"></i>
            <span
              >总点赞数：<strong>{{ totalLikes }}</strong></span
            >
          </div> -->
        </div>
      </div>
    </main>

    <!-- 灯箱模态框 -->
    <div v-if="lightboxPhoto" class="lightbox-overlay" @click="closeLightbox">
      <div class="lightbox-content" @click.stop>
        <button class="lightbox-close" @click="closeLightbox">
          <i class="fas fa-times"></i>
        </button>
        <img
          :src="lightboxPhoto.url"
          :alt="lightboxPhoto.title"
          class="lightbox-image"
        />
        <div class="lightbox-info">
          <h3>{{ lightboxPhoto.title }}</h3>
          <p>{{ lightboxPhoto.description }}</p>
          <p class="photo-date">{{ lightboxPhoto.date }}</p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getList } from "@/api/photoalbum/photoAlbum";
import apiMixin from "@/mixins/apiMixin";
import { dataTransformers } from "@/utils/apiHelper";
import LoadingIndicator from "@/components/LoadingIndicator.vue";
import ErrorMessage from "@/components/ErrorMessage.vue";
import { getDictionary } from "@/api/system/dictbiz";
import { add } from "@/api/photoalbum/photoAlbum";

export default {
  name: "Photo",
  mixins: [apiMixin],
  components: {
    LoadingIndicator,
    ErrorMessage,
  },
  data() {
    return {
      mainTitle: "",
      subTitle: "",
      photos: [],
      activeCategory: "all",
      lightboxPhoto: null,
      dataSource: "unknown",
      responseTime: 0,
      isUploading: false, // 上传状态
      maxUploadSizeMB: 10,
    };
  },
  computed: {
    filteredPhotos() {
      if (this.activeCategory === "all") {
        return this.photos;
      }
      return this.photos.filter(
        (photo) => photo.category === this.activeCategory
      );
    },
    totalPhotos() {
      return this.filteredPhotos.length;
    },
    totalLikes() {
      return this.photos.reduce((sum, photo) => sum + photo.likes, 0);
    },
  },
  async mounted() {
    await this.loadPhotosData();
    await this.loadData();
  },
  methods: {
    async loadData() {
      const response = await getDictionary({
        code: "hy_photo",
      });
      if (response && response.data && response.data.success) {
        const dictData = response.data.data;
        if (dictData && Array.isArray(dictData) && dictData.length > 0) {
          this.mainTitle = dictData.find(
            (item) => item.dictValue === "主标题"
          ).dictKey;
          this.subTitle = dictData.find(
            (item) => item.dictValue === "副标题"
          ).dictKey;
        }
      } else {
        throw new Error("API响应格式不正确");
      }
    },
    async loadPhotosData() {
      const startTime = Date.now();

      try {
        const response = await getList(1, 20, {});
        if (response && response.data && response.data.success) {
          const transformedData = dataTransformers.photos(response.data);
          this.photos = transformedData;
          this.dataSource = "api";
          this.hasError = false;
          this.errorMessage = "";
        } else {
          throw new Error("API响应格式不正确");
        }
        this.responseTime = Date.now() - startTime;
      } catch (error) {
        console.error("加载照片数据失败:", error);
        this.photos = this.defaultPhotosData;
        this.dataSource = "fallback";
        this.hasError = true;
        this.errorMessage = error.message || "数据加载失败，使用默认数据";
        this.responseTime = Date.now() - startTime;
      }
    },
    async refreshData() {
      await this.loadPhotosData();
    },
    formatApiData(data, type) {
      if (type === "array" && Array.isArray(data)) {
        return dataTransformers.photos(data);
      }
      return data;
    },
    switchCategory(category) {
      this.activeCategory = category;
    },
    openLightbox(photo) {
      this.lightboxPhoto = photo;
    },
    closeLightbox() {
      this.lightboxPhoto = null;
    },
    likePhoto(photo) {
      if (!photo.liked) {
        photo.liked = true;
        photo.likes++;
      }
    },
    downloadPhoto(photo) {
      alert(`下载照片：${photo.title}\n（演示功能）`);
    },
  },
};
</script>
<style scoped>
/* 页面通用样式 */
.page-container {
  width: 100%;
  max-height: 85vh;
  background-size: cover;
  position: relative;
  z-index: 1;
  margin: 0 auto;
  box-sizing: border-box;
}

.page-content {
  margin-top: 15px;
}

/* 容器样式 - 统一磨砂玻璃效果 */
.list-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  border-radius: 15px;
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.05),
    0 10px 25px rgba(0, 0, 0, 0.3);
  animation: slideInUp 0.6s ease forwards;
  max-width: 1400px;
  margin: 0 auto;
  max-height: 85vh;
}

/* 标题区域样式 */
.form-header {
  text-align: center;
  margin-bottom: 25px;
  color: #ffffff;
}

.icon-with-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10px;
}

.form-header i {
  font-size: 36px;
  color: #07d3f0;
  margin-bottom: 8px;
  text-shadow: 0 0 15px rgba(7, 211, 240, 0.6);
}

.icon-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.form-header h2 {
  color: #ffffff;
  font-size: 24px;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.form-header p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  padding: 0 10px;
}

/* 相册分类按钮样式 */
.album-categories {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;
  padding: 0 5px;
  flex-wrap: wrap;
  gap: 8px;
}

.category-btn {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(7, 211, 240, 0.3);
  color: #07d3f0;
  padding: 8px 6px;
  border-radius: 20px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  flex: 1;
  min-width: 80px;
  text-align: center;
}

.category-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.category-btn:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.category-btn.active,
.category-btn:hover {
  background: rgba(7, 211, 240, 0.2);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(7, 211, 240, 0.2);
}

/* 上传按钮样式 */
.upload-section {
  text-align: center;
  margin: 20px 0;
}

.upload-btn {
  background: linear-gradient(
    135deg,
    rgba(7, 211, 240, 0.3),
    rgba(7, 211, 240, 0.15)
  );
  color: white;
  border: 1px solid rgba(7, 211, 240, 0.3);
  border-radius: 10px;
  padding: 12px 24px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.upload-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.upload-btn:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.upload-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(7, 211, 240, 0.2);
}

/* 照片滚动容器 */
.photos-scroll-container {
  max-height: calc(85vh - 360px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-right: 5px;
  margin-bottom: 20px;
}

/* 滚动条美化 */
.photos-scroll-container::-webkit-scrollbar {
  width: 5px;
}

.photos-scroll-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.photos-scroll-container::-webkit-scrollbar-thumb {
  background: rgba(7, 211, 240, 0.5);
  border-radius: 10px;
}

.photos-scroll-container::-webkit-scrollbar-thumb:hover {
  background: rgba(7, 211, 240, 0.8);
}

/* 照片网格 */
.photo-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin: 10px 0;
}

/* 照片项样式 */
.photo-item {
  position: relative;
  border-radius: 10px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.photo-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.photo-item:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.photo-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.09);
}

.photo-img {
  width: 100%;
  height: 120px;
  object-fit: cover;
}

.photo-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  padding: 15px 10px 10px;
  transform: translateY(100%);
  transition: all 0.3s ease;
}

.photo-item:hover .photo-overlay {
  transform: translateY(0);
}

.photo-info h3 {
  font-size: 12px;
  margin-bottom: 3px;
}

.photo-info p {
  font-size: 10px;
  opacity: 0.8;
  margin-bottom: 8px;
}

.photo-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 3px;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.action-btn .fa-heart.liked {
  color: #ff4757;
}

/* 统计信息样式 */
.photo-stats {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 15px;
  margin-top: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.stat-item i {
  color: #07d3f0;
  text-shadow: 0 0 10px rgba(7, 211, 240, 0.5);
}

.stat-item strong {
  color: #ffffff;
}

/* 灯箱样式 */
.lightbox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.lightbox-content {
  position: relative;
  max-width: 90%;
  max-height: 90%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.lightbox-close {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(7, 211, 240, 0.3);
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lightbox-image {
  width: 100%;
  height: auto;
  max-height: 70vh;
  object-fit: contain;
}

.lightbox-info {
  padding: 15px;
  color: white;
}

.lightbox-info h3 {
  color: #ffffff;
  margin-bottom: 8px;
}

.lightbox-info p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin-bottom: 5px;
}

.photo-date {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}
</style>
