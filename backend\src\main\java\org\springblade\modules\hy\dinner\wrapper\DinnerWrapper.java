/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 * <p>
 * Use of this software is governed by the Commercial License Agreement
 * obtained after purchasing a license from BladeX.
 * <p>
 * 1. This software is for development use only under a valid license
 * from BladeX.
 * <p>
 * 2. Redistribution of this software's source code to any third party
 * without a commercial license is strictly prohibited.
 * <p>
 * 3. Licensees may copyright their own code but cannot use segments
 * from this software for such purposes. Copyright of this software
 * remains with BladeX.
 * <p>
 * Using this software signifies agreement to this License, and the software
 * must not be used for illegal purposes.
 * <p>
 * THIS SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY. The author is
 * not liable for any claims arising from secondary or illegal development.
 * <p>
 * Author: <PERSON><PERSON> (<EMAIL>)
 */
package org.springblade.modules.hy.dinner.wrapper;

import org.springblade.core.mp.support.BaseEntityWrapper;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.modules.hy.dinner.pojo.entity.DinnerEntity;
import org.springblade.modules.hy.dinner.pojo.vo.DinnerVO;
import java.util.Map;
import java.util.Objects;

/**
 * 用餐管理表 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
public class DinnerWrapper extends BaseEntityWrapper<DinnerEntity, DinnerVO>  {

	public static DinnerWrapper build() {
		return new DinnerWrapper();
 	}

	@Override
	public DinnerVO entityVO(DinnerEntity dinner) {
		DinnerVO dinnerVO = Objects.requireNonNull(BeanUtil.copyProperties(dinner, DinnerVO.class));

		//User createUser = UserCache.getUser(dinner.getCreateUser());
		//User updateUser = UserCache.getUser(dinner.getUpdateUser());
		//dinnerVO.setCreateUserName(createUser.getName());
		//dinnerVO.setUpdateUserName(updateUser.getName());

		return dinnerVO;
	}

	/**
	 * 查询条件处理
	 */
	public void dinnerQuery(Map<String, Object> dinner) {
		// 此场景仅在 pg数据库 map类型传参的情况下需要处理，entity传参已经包含数据类型，则无需关心
		// 针对 pg数据库 Long类型字段查询需要强转的处理
		String userIdKey = "userId_equal";
		if (Func.isNotEmpty(dinner.get(userIdKey))) {
			// 数据库字段为bigint类型，设置"="查询，具体查询参数请见 @org.springblade.core.mp.support.SqlKeyword
			dinner.put(userIdKey, Func.toLong(dinner.get(userIdKey)));
		}
	}

}
