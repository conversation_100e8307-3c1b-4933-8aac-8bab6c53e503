<template>
  <div class="simple-test">
    <h1>🧪 简单测试页面</h1>
    <p>如果您能看到这个页面，说明路由配置正常工作！</p>
    
    <div class="test-links">
      <h3>测试链接：</h3>
      <ul>
        <li>
          <router-link to="/mobile-auth-test">📱 Mobile登录守卫测试</router-link>
        </li>
        <li>
          <router-link to="/dev-tools">🛠️ 开发工具</router-link>
        </li>
        <li>
          <router-link to="/mobile">📲 Mobile页面（会触发登录守卫）</router-link>
        </li>
        <li>
          <router-link to="/dinglogin">🔐 钉钉登录页面</router-link>
        </li>
      </ul>
    </div>
    
    <div class="quick-actions">
      <h3>快速操作：</h3>
      <button @click="simulateLogin" class="btn btn-success">模拟登录</button>
      <button @click="simulateLogout" class="btn btn-warning">模拟登出</button>
      <button @click="checkAuth" class="btn btn-info">检查登录状态</button>
    </div>
    
    <div class="current-status">
      <h3>当前状态：</h3>
      <p>登录状态: <span :class="authStatusClass">{{ authStatusText }}</span></p>
      <p>当前路由: {{ $route.path }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SimpleTest',
  data() {
    return {
      authStatus: false
    }
  },
  computed: {
    authStatusText() {
      return this.authStatus ? '已登录' : '未登录'
    },
    authStatusClass() {
      return this.authStatus ? 'status-success' : 'status-error'
    }
  },
  methods: {
    simulateLogin() {
      const mockUserInfo = {
        access_token: 'mock_token_' + Date.now(),
        user_id: 'test_user_123',
        user_name: 'Test User',
        account: 'testuser'
      };
      localStorage.setItem('userInfo', JSON.stringify(mockUserInfo));
      this.checkAuth();
      alert('模拟登录成功！');
    },
    
    simulateLogout() {
      localStorage.removeItem('userInfo');
      this.checkAuth();
      alert('模拟登出成功！');
    },
    
    checkAuth() {
      try {
        const userInfo = localStorage.getItem('userInfo');
        if (userInfo) {
          const parsed = JSON.parse(userInfo);
          this.authStatus = !!(parsed.access_token && parsed.user_id);
        } else {
          this.authStatus = false;
        }
      } catch (e) {
        this.authStatus = false;
      }
    }
  },
  
  mounted() {
    this.checkAuth();
  }
}
</script>

<style scoped>
.simple-test {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

h1 {
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}

.test-links, .quick-actions, .current-status {
  margin: 30px 0;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.test-links ul {
  list-style: none;
  padding: 0;
}

.test-links li {
  margin: 10px 0;
}

.test-links a {
  color: #007bff;
  text-decoration: none;
  font-size: 16px;
}

.test-links a:hover {
  text-decoration: underline;
}

.btn {
  padding: 8px 16px;
  margin: 5px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-success { background: #28a745; color: white; }
.btn-warning { background: #ffc107; color: black; }
.btn-info { background: #17a2b8; color: white; }

.btn:hover {
  opacity: 0.8;
}

.status-success {
  color: #28a745;
  font-weight: bold;
}

.status-error {
  color: #dc3545;
  font-weight: bold;
}

h3 {
  color: #333;
  margin-bottom: 15px;
}
</style>
