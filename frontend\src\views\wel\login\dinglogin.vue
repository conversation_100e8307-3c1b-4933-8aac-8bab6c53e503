<template>
  <div class="mobile-login-container">
    <!-- 标题区域 -->
    <section class="title-section">
      <div class="logo-section">
        <!-- <div class="logo-icon">
          <i class="fas fa-calendar-check"></i>
        </div> -->
        <h1 class="main-title">智慧会务系统</h1>
      </div>
    </section>

    <!-- 登录卡片 -->
    <section class="login-section">
      <div class="login-card">
        <div class="welcome-text">
          <h3>欢迎使用</h3>
          <p>请使用钉钉账号登录以继续</p>
        </div>

        <div class="login-button-container">
          <button class="login-button" @click="handleClick()">
            <i class="fas fa-sign-in-alt"></i>
            <span>钉钉授权登录</span>
          </button>
        </div>


      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import * as dd from 'dingtalk-jsapi'
import { useUserStore } from '@/store/mobile/user'

const router = useRouter()
const userStore = useUserStore()
const loading = ref(false)
const loginFailDialog = ref(false)

// 钉钉登录点击处理
const handleClick = async () => {
  loading.value = true
  try {
    await userStore.login()
    // 登录成功后跳转到原来要访问的页面
    handleLoginSuccess()
  } catch (error) {
    console.error('登录失败:', error)
    loginFailDialog.value = true
    loading.value = false
  }
}

// 登录成功后的跳转处理
const handleLoginSuccess = () => {
  // 获取保存的重定向路径
  const redirectPath = localStorage.getItem('redirectPath') || '/mobile'
  // 清除保存的路径
  localStorage.removeItem('redirectPath')
  console.log('钉钉登录成功，跳转到:', redirectPath)
  // 跳转到目标页面
  router.push(redirectPath)
}

const handleLogin = async () => {
  loading.value = true
  try {
    await userStore.login()
    // 登录成功后的跳转由handleLoginSuccess函数处理
    handleLoginSuccess()
  } catch (error) {
    console.error('登录失败:', error)
    loginFailDialog.value = true
    loading.value = false
  }
}

const handleFailDialogClose = () => {
  loginFailDialog.value = false
}

// 组件挂载时检查是否已经登录
onMounted(() => {
  // 如果用户已经登录，直接跳转
  if (userStore.isLogin) {
    handleLoginSuccess()
  }
})
</script>

<style scoped>
/* 移动端登录页面样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.mobile-login-container {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  min-height: 100vh;
  background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

/* 标题区域 */
.title-section {
  text-align: center;
  margin-bottom: 60px;
  color: white;
  animation: fadeInDown 0.8s ease forwards;
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.logo-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.logo-icon i {
  font-size: 36px;
  color: white;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.logo-icon i::before {
  content: "📅";
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
}

.main-title {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
  letter-spacing: 2px;
}

.sub-title {
  font-size: 16px;
  font-weight: 400;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
  letter-spacing: 1px;
}

/* 登录区域 */
.login-section {
  width: 100%;
  max-width: 350px;
  animation: slideInUp 0.8s ease forwards;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  padding: 40px 30px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  text-align: center;
}

.welcome-text {
  margin-bottom: 40px;
}

.welcome-text h3 {
  font-size: 24px;
  color: #333;
  margin-bottom: 10px;
  font-weight: 600;
}

.welcome-text p {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* 登录按钮 */
.login-button-container {
  margin-bottom: 30px;
}

.login-button {
  width: 100%;
  height: 50px;
  background: linear-gradient(135deg, #4682B4, #1E90FF);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(70, 130, 180, 0.3);
  position: relative;
  overflow: hidden;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.login-button:hover::before {
  left: 100%;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(70, 130, 180, 0.4);
}

.login-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(70, 130, 180, 0.3);
}

.login-button i {
  font-size: 18px;
}

.login-button i::before {
  content: "🔑";
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
}

/* 提示信息 */
.tips {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 20px;
}

.tips p {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}



/* 动画效果 */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .mobile-login-container {
    padding: 15px;
  }

  .login-card {
    padding: 30px 20px;
  }

  .main-title {
    font-size: 24px;
  }

  .sub-title {
    font-size: 14px;
  }

  .welcome-text h3 {
    font-size: 20px;
  }

  .login-button {
    height: 45px;
    font-size: 15px;
  }
}

@media (max-width: 360px) {
  .logo-icon {
    width: 70px;
    height: 70px;
  }

  .logo-icon i {
    font-size: 32px;
  }

  .main-title {
    font-size: 22px;
  }
}
</style>
