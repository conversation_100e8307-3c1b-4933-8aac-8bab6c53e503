import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/hy/dinner/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/hy/dinner/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/hy/dinner/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/hy/dinner/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/hy/dinner/submit',
    method: 'post',
    data: row
  })
}

// 根据用户ID获取用餐记录
export const getByUserId = (userId) => {
  return request({
    url: '/hy/dinner/list',
    method: 'get',
    params: {
      userId_equal: userId
    }
  })
}
